'use client';

import { useState, useEffect } from 'react';
import { 
  FileText, 
  Download, 
  Calendar, 
  Filter,
  BarChart3,
  TrendingUp,
  DollarSign,
  Users,
  Target,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { dashboardApi, farmApi, simulationApi } from '@/services/api';
import { Farm } from '@/types';

export default function ReportsPage() {
  const [farms, setFarms] = useState<Farm[]>([]);
  const [selectedFarmId, setSelectedFarmId] = useState<string>('');
  const [reportType, setReportType] = useState<string>('overview');
  const [dateRange, setDateRange] = useState<string>('30');
  const [reportData, setReportData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    loadFarms();
  }, []);

  useEffect(() => {
    if (selectedFarmId && reportType) {
      generateReport();
    }
  }, [selectedFarmId, reportType, dateRange]);

  const loadFarms = async () => {
    try {
      const farmsData = await farmApi.getFarms();
      setFarms(farmsData);
      if (farmsData.length > 0) {
        setSelectedFarmId(farmsData[0].id);
      }
    } catch (err) {
      setError('Çiftlikler yüklenirken hata oluştu');
      console.error(err);
    }
  };

  const generateReport = async () => {
    if (!selectedFarmId) return;
    
    setLoading(true);
    setError('');
    
    try {
      let data;
      
      switch (reportType) {
        case 'overview':
          data = await dashboardApi.getOverview(selectedFarmId);
          break;
        case 'financial':
          data = await dashboardApi.getFinancial(selectedFarmId);
          break;
        case 'performance':
          data = await dashboardApi.getPerformance(selectedFarmId);
          break;
        case 'simulations':
          data = await simulationApi.getSimulationsByFarm(selectedFarmId);
          break;
        default:
          data = await dashboardApi.getOverview(selectedFarmId);
      }
      
      setReportData(data);
    } catch (err) {
      setError('Rapor oluşturulurken hata oluştu');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const exportReport = () => {
    if (!reportData) return;
    
    const reportContent = JSON.stringify(reportData, null, 2);
    const blob = new Blob([reportContent], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${reportType}-raporu-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount);
  };

  const formatNumber = (num: number, decimals: number = 0) => {
    return new Intl.NumberFormat('tr-TR', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(num);
  };

  const getReportTitle = () => {
    const titles: { [key: string]: string } = {
      'overview': 'Genel Bakış Raporu',
      'financial': 'Finansal Analiz Raporu',
      'performance': 'Performans Raporu',
      'simulations': 'Simülasyon Raporu'
    };
    return titles[reportType] || 'Rapor';
  };

  const renderOverviewReport = () => {
    if (!reportData) return null;
    
    return (
      <div className="space-y-6">
        {/* Özet Kartlar */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="content-overlay p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Users className="h-5 w-5 text-blue-600" />
              <span className="text-sm font-medium text-gray-700">Toplam Hayvan</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {reportData.animal_stats.total_animals}
            </p>
            <p className="text-sm text-gray-600">
              {reportData.animal_stats.male_count} Erkek, {reportData.animal_stats.female_count} Dişi
            </p>
          </div>
          
          <div className="content-overlay p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Target className="h-5 w-5 text-green-600" />
              <span className="text-sm font-medium text-gray-700">Aktif Rasyonlar</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {reportData.ration_stats.active_rations}
            </p>
            <p className="text-sm text-gray-600">
              {reportData.ration_stats.total_rations} toplam rasyon
            </p>
          </div>
          
          <div className="content-overlay p-4">
            <div className="flex items-center space-x-2 mb-2">
              <BarChart3 className="h-5 w-5 text-purple-600" />
              <span className="text-sm font-medium text-gray-700">Simülasyonlar</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {reportData.simulation_stats.total_simulations}
            </p>
            <p className="text-sm text-gray-600">
              Ortalama {formatCurrency(reportData.simulation_stats.avg_simulation_cost || 0)}
            </p>
          </div>
          
          <div className="content-overlay p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Clock className="h-5 w-5 text-orange-600" />
              <span className="text-sm font-medium text-gray-700">Son 30 Gün</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {reportData.recent_activity.new_animals_30d + 
               reportData.recent_activity.new_rations_30d + 
               reportData.recent_activity.new_simulations_30d}
            </p>
            <p className="text-sm text-gray-600">Yeni kayıt</p>
          </div>
        </div>
        
        {/* Detaylı İstatistikler */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="content-overlay p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Hayvan İstatistikleri</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Ortalama Ağırlık:</span>
                <span className="font-medium">{formatNumber(reportData.animal_stats.avg_weight, 1)} kg</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Ortalama Yaş:</span>
                <span className="font-medium">{formatNumber(reportData.animal_stats.avg_age_months, 0)} ay</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Erkek Oranı:</span>
                <span className="font-medium">
                  %{formatNumber((reportData.animal_stats.male_count / reportData.animal_stats.total_animals) * 100, 1)}
                </span>
              </div>
            </div>
          </div>
          
          <div className="content-overlay p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Yem İstatistikleri</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Toplam Yem Türü:</span>
                <span className="font-medium">{reportData.feed_stats.total_feeds}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Ortalama Maliyet:</span>
                <span className="font-medium">{formatCurrency(reportData.feed_stats.avg_cost_per_kg)}/kg</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Konsantre Yem:</span>
                <span className="font-medium">{reportData.feed_stats.concentrate_count} adet</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderFinancialReport = () => {
    if (!reportData) return null;
    
    return (
      <div className="space-y-6">
        {/* Maliyet Kartları */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="content-overlay p-4">
            <div className="flex items-center space-x-2 mb-2">
              <DollarSign className="h-5 w-5 text-green-600" />
              <span className="text-sm font-medium text-gray-700">Günlük Maliyet</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {formatCurrency(reportData.daily_costs.total_daily_cost)}
            </p>
          </div>
          
          <div className="content-overlay p-4">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <span className="text-sm font-medium text-gray-700">Aylık Maliyet</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {formatCurrency(reportData.daily_costs.monthly_cost)}
            </p>
          </div>
          
          <div className="content-overlay p-4">
            <div className="flex items-center space-x-2 mb-2">
              <BarChart3 className="h-5 w-5 text-purple-600" />
              <span className="text-sm font-medium text-gray-700">Yıllık Maliyet</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {formatCurrency(reportData.daily_costs.yearly_cost)}
            </p>
          </div>
          
          <div className="content-overlay p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Target className="h-5 w-5 text-orange-600" />
              <span className="text-sm font-medium text-gray-700">Günlük KM</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {formatNumber(reportData.daily_costs.total_daily_dm_kg, 1)} kg
            </p>
          </div>
        </div>
        
        {/* Aktif Rasyonlar */}
        {reportData.active_rations && reportData.active_rations.length > 0 && (
          <div className="content-overlay p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Aktif Rasyonlar</h3>
            <div className="space-y-3">
              {reportData.active_rations.map((ration: any, index: number) => (
                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{ration.name}</p>
                    <p className="text-sm text-gray-600">{formatNumber(ration.daily_dm_kg, 1)} kg KM/gün</p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">{formatCurrency(ration.daily_cost)}</p>
                    <p className="text-sm text-gray-600">günlük</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderPerformanceReport = () => {
    if (!reportData) return null;
    
    return (
      <div className="space-y-6">
        {/* Irk Performansı */}
        {reportData.breed_performance && reportData.breed_performance.length > 0 && (
          <div className="content-overlay p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Irk Performansı</h3>
            <div className="space-y-3">
              {reportData.breed_performance.map((breed: any, index: number) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium text-gray-900">{breed.breed}</h4>
                    <span className="text-sm text-gray-600">{breed.animal_count} hayvan</span>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Ortalama Ağırlık:</span>
                      <span className="font-medium ml-2">{formatNumber(breed.avg_weight, 0)} kg</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Ortalama VKS:</span>
                      <span className="font-medium ml-2">{formatNumber(breed.avg_bcs, 1)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* En İyi Performanslar */}
        {reportData.best_performers && reportData.best_performers.length > 0 && (
          <div className="content-overlay p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">En İyi Performanslar</h3>
            <div className="space-y-3">
              {reportData.best_performers.map((performer: any, index: number) => (
                <div key={index} className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium text-green-900">{performer.name}</h4>
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                      #{index + 1}
                    </span>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-green-600">FCR:</span>
                      <span className="font-medium ml-2">{formatNumber(performer.feed_conversion_ratio, 1)}</span>
                    </div>
                    <div>
                      <span className="text-green-600">Maliyet:</span>
                      <span className="font-medium ml-2">{formatCurrency(performer.cost_per_kg_gain)}/kg</span>
                    </div>
                    <div>
                      <span className="text-green-600">Günlük Artış:</span>
                      <span className="font-medium ml-2">{formatNumber(performer.average_daily_gain_kg, 3)} kg</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderSimulationsReport = () => {
    if (!reportData || !Array.isArray(reportData)) return null;
    
    return (
      <div className="space-y-6">
        <div className="content-overlay p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Simülasyon Listesi ({reportData.length} adet)
          </h3>
          <div className="space-y-3">
            {reportData.map((simulation: any, index: number) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-medium text-gray-900">{simulation.name}</h4>
                  <span className="text-sm text-gray-600">
                    {simulation.breed} - {simulation.gender === 'male' ? 'Erkek' : 'Dişi'}
                  </span>
                </div>
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Toplam Maliyet:</span>
                    <span className="font-medium ml-2">{formatCurrency(simulation.total_feed_cost)}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Toplam Yem:</span>
                    <span className="font-medium ml-2">{formatNumber(simulation.total_feed_consumption_kg, 0)} kg</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Yaş Aralığı:</span>
                    <span className="font-medium ml-2">{simulation.start_age_months}-{simulation.end_age_months} ay</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Oluşturulma:</span>
                    <span className="font-medium ml-2">
                      {new Date(simulation.created_at).toLocaleDateString('tr-TR')}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderReportContent = () => {
    switch (reportType) {
      case 'overview':
        return renderOverviewReport();
      case 'financial':
        return renderFinancialReport();
      case 'performance':
        return renderPerformanceReport();
      case 'simulations':
        return renderSimulationsReport();
      default:
        return renderOverviewReport();
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Raporlar</h1>
          <p className="text-gray-600 mt-2">
            Detaylı analiz ve raporlama
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="content-overlay p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Çiftlik
            </label>
            <select
              value={selectedFarmId}
              onChange={(e) => setSelectedFarmId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              {farms.map((farm) => (
                <option key={farm.id} value={farm.id}>
                  {farm.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Rapor Türü
            </label>
            <select
              value={reportType}
              onChange={(e) => setReportType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="overview">Genel Bakış</option>
              <option value="financial">Finansal Analiz</option>
              <option value="performance">Performans</option>
              <option value="simulations">Simülasyonlar</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Zaman Aralığı
            </label>
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="7">Son 7 Gün</option>
              <option value="30">Son 30 Gün</option>
              <option value="90">Son 3 Ay</option>
              <option value="365">Son 1 Yıl</option>
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={exportReport}
              disabled={!reportData || loading}
              className="w-full btn-primary text-white py-2 rounded-lg flex items-center justify-center space-x-2 disabled:opacity-50"
            >
              <Download className="h-4 w-4" />
              <span>Dışa Aktar</span>
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Report Content */}
      <div className="content-overlay p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900">{getReportTitle()}</h2>
          {reportData && (
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Rapor oluşturuldu: {new Date().toLocaleString('tr-TR')}</span>
            </div>
          )}
        </div>

        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Rapor oluşturuluyor...</p>
          </div>
        ) : (
          renderReportContent()
        )}
      </div>
    </div>
  );
}
