#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON>van <PERSON> - Sahte Veri Oluşturucu
Bu script veritabanına gerçekçi sahte veriler ekler.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.database import SessionLocal, create_tables
from app.database.models import Farm, Feed, Animal
import random
from datetime import datetime, timedelta

def get_db_session():
    """Veritabanı session oluştur"""
    return SessionLocal()

def main():
    """Ana fonksiyon"""
    print("🚀 Sahte veri oluşturma başlıyor...")

    # Önce tabloları oluştur
    create_tables()

    db = get_db_session()

    try:
        # Çiftlik oluştur
        farm = Farm(
            name='Teslime Hanım Çiftliği',
            location='Bozdoğan, Aydın',
            established_date=datetime.strptime('2020-03-15', '%Y-%m-%d').date(),
            total_land_hectares=150.0,
            pasture_land_hectares=80.0,
            barn_capacity=200,
            feed_storage_capacity_tons=500.0,
            silage_capacity_tons=1000.0,
            hay_storage_capacity_tons=300.0,
            water_storage_capacity_liters=50000.0,
            milking_parlor_capacity=50,
            quarantine_facility_capacity=10,
            hospital_pen_capacity=5,
            handling_facility_present=True,
            scale_capacity_kg=1000.0,
            # Maliyet bilgileri
            labor_cost_monthly=15000.0,
            electricity_cost_monthly=2500.0,
            water_cost_monthly=800.0,
            fuel_cost_monthly=3000.0,
            insurance_cost_monthly=1200.0,
            maintenance_cost_monthly=2000.0,
            veterinary_cost_annual=8000.0,
            taxation_annual=5000.0,
            depreciation_annual=25000.0,
            interest_cost_annual=12000.0,
            # Hayvan başına maliyetler
            vaccination_cost_per_animal=150.0,
            deworming_cost_per_animal=50.0,
            hoof_care_cost_per_animal=80.0,
            breeding_cost_per_service=300.0,
            # Fiyat bilgileri
            live_cattle_price_per_kg=45.0,
            calf_price_per_head=8000.0,
            bull_price_per_head=25000.0,
            cow_price_per_head=18000.0,
            carcass_price_per_kg=55.0,
            manure_price_per_ton=150.0,
            hide_price_per_piece=200.0,
            # Performans oranları
            conception_rate=0.85,
            calving_rate=0.90,
            calf_survival_rate=0.95,
            weaning_rate=0.92,
            mortality_rate_adult=0.02,
            mortality_rate_young=0.05,
            culling_rate=0.15,
            replacement_rate=0.20,
            # Risk faktörleri
            disease_outbreak_probability=0.10,
            market_volatility_coefficient=0.15,
            weather_risk_probability=0.20,
            feed_price_volatility=0.12,
            drought_probability=0.08,
            flood_probability=0.03,
            policy_change_risk=0.05,
            # Simülasyon parametreleri
            simulation_duration_years=5,
            time_step_days=1,
            monte_carlo_iterations=1000,
            confidence_interval=0.95,
            discount_rate=0.08,
            inflation_rate=0.06,
            tax_rate=0.20
        )
        db.add(farm)
        db.flush()

        # Yemler oluştur
        feeds_data = [
            {'name': 'Süt İneği Konsantresi', 'feed_type': 'concentrate', 'cost_per_kg': 8.50, 'dry_matter_percentage': 88.0, 'crude_protein_percentage': 18.0, 'metabolizable_energy_mcal_kg': 2.8, 'storage_life_days': 180, 'moisture_content_percentage': 12.0},
            {'name': 'Mısır Silajı', 'feed_type': 'silage', 'cost_per_kg': 1.20, 'dry_matter_percentage': 35.0, 'crude_protein_percentage': 8.0, 'metabolizable_energy_mcal_kg': 2.4, 'storage_life_days': 365, 'moisture_content_percentage': 65.0},
            {'name': 'Yonca Kuru Otu', 'feed_type': 'hay', 'cost_per_kg': 2.80, 'dry_matter_percentage': 88.0, 'crude_protein_percentage': 18.0, 'metabolizable_energy_mcal_kg': 2.2, 'storage_life_days': 730, 'moisture_content_percentage': 12.0},
            {'name': 'Arpa', 'feed_type': 'grain', 'cost_per_kg': 4.20, 'dry_matter_percentage': 86.0, 'crude_protein_percentage': 11.5, 'metabolizable_energy_mcal_kg': 2.5, 'storage_life_days': 365, 'moisture_content_percentage': 14.0},
            {'name': 'Buğday Kepeği', 'feed_type': 'byproduct', 'cost_per_kg': 3.50, 'dry_matter_percentage': 88.0, 'crude_protein_percentage': 16.0, 'metabolizable_energy_mcal_kg': 2.2, 'storage_life_days': 90, 'moisture_content_percentage': 12.0},
            {'name': 'Soya Küspesi', 'feed_type': 'protein', 'cost_per_kg': 15.00, 'dry_matter_percentage': 89.0, 'crude_protein_percentage': 44.0, 'metabolizable_energy_mcal_kg': 2.4, 'storage_life_days': 180, 'moisture_content_percentage': 11.0},
        ]

        for feed_data in feeds_data:
            feed = Feed(farm_id=farm.id, **feed_data)
            db.add(feed)

        # Hayvanlar oluştur
        breeds = ['holstein', 'angus', 'simmental']
        for i in range(30):
            breed = random.choice(breeds)
            gender = random.choice(['male', 'female'])
            age_months = random.randint(12, 60)
            birth_date = (datetime.now() - timedelta(days=age_months * 30.44)).date()

            # Irk özelliklerine göre ağırlık
            if breed == 'holstein':
                base_weight = 650 if gender == 'female' else 900
            elif breed == 'angus':
                base_weight = 550 if gender == 'female' else 850
            else:  # simmental
                base_weight = 750 if gender == 'female' else 1100

            # Yaşa göre ağırlık ayarlama
            age_factor = min(age_months / 24, 1.0)
            current_weight = base_weight * (0.3 + 0.7 * age_factor) + random.uniform(-50, 50)
            current_weight = max(current_weight, 50)

            # Durum belirleme
            if age_months < 12:
                status = 'calf'
            elif gender == 'female' and breed == 'holstein':
                status = random.choice(['breeding', 'milking', 'dry'])
            else:
                status = random.choice(['breeding', 'fattening'])

            # Gebelik durumu (sadece dişiler için)
            is_pregnant = False
            pregnancy_start_date = None
            expected_calving_date = None

            if gender == 'female' and age_months > 15 and random.random() < 0.3:  # %30 gebelik oranı
                is_pregnant = True
                pregnancy_days = random.randint(50, 250)  # Gebelik süresi
                pregnancy_start_date = (datetime.now() - timedelta(days=pregnancy_days)).date()
                expected_calving_date = (datetime.now() + timedelta(days=283 - pregnancy_days)).date()

            animal = Animal(
                farm_id=farm.id,
                tag=f"{breed.upper()[:3]}-{i+1:03d}",
                breed=breed,
                birth_date=birth_date,
                gender=gender,
                current_weight_kg=round(current_weight, 1),
                body_condition_score=round(random.uniform(2.5, 4.5), 1),
                status=status,
                is_pregnant=is_pregnant,
                pregnancy_start_date=pregnancy_start_date,
                expected_calving_date=expected_calving_date,
                purchase_price=random.uniform(8000, 25000) if random.random() < 0.7 else None,
                purchase_date=(datetime.now() - timedelta(days=random.randint(30, 1000))).date() if random.random() < 0.7 else None,
                notes=random.choice(['', 'Sağlıklı hayvan', 'Düzenli kontrol gerekli', 'Yüksek verimli', 'Özel bakım gerekli'])
            )
            db.add(animal)

        db.commit()
        print("✅ Sahte veri oluşturma tamamlandı!")
        print("📊 1 çiftlik, 6 yem türü, 30 hayvan oluşturuldu")

    except Exception as e:
        db.rollback()
        print(f"✗ Hata: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    main()
