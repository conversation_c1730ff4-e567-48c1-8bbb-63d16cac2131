#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON>van <PERSON> - Sahte Veri Oluşturucu
Bu script veritabanına gerçekçi sahte veriler ekler.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.database import SessionLocal, create_tables
from app.database.models import Farm, Feed, Animal
import random
from datetime import datetime, timedelta

def get_db_session():
    """Veritabanı session oluştur"""
    return SessionLocal()

def main():
    """Ana fonksiyon"""
    print("🚀 Sahte veri oluşturma başlıyor...")

    # Önce tabloları oluştur
    create_tables()

    db = get_db_session()

    try:
        # Çiftlik oluştur
        farm = Farm(
            name='Teslime Hanım Çiftliği',
            location='Bozdoğan, Aydın',
            established_date=datetime.strptime('2020-03-15', '%Y-%m-%d').date(),
            total_land_hectares=150.0,
            pasture_land_hectares=80.0,
            barn_capacity=200,
            feed_storage_capacity_tons=500.0,
            silage_capacity_tons=1000.0,
            hay_storage_capacity_tons=300.0,
            water_storage_capacity_liters=50000.0,
            milking_parlor_capacity=50,
            quarantine_facility_capacity=10,
            hospital_pen_capacity=5,
            handling_facility_present=True,
            scale_capacity_kg=1000.0,
            # Maliyet bilgileri
            labor_cost_monthly=15000.0,
            electricity_cost_monthly=2500.0,
            water_cost_monthly=800.0,
            fuel_cost_monthly=3000.0,
            insurance_cost_monthly=1200.0,
            maintenance_cost_monthly=2000.0,
            veterinary_cost_annual=8000.0,
            taxation_annual=5000.0,
            depreciation_annual=25000.0,
            interest_cost_annual=12000.0,
            # Hayvan başına maliyetler
            vaccination_cost_per_animal=150.0,
            deworming_cost_per_animal=50.0,
            hoof_care_cost_per_animal=80.0,
            breeding_cost_per_service=300.0,
            # Fiyat bilgileri
            live_cattle_price_per_kg=45.0,
            calf_price_per_head=8000.0,
            bull_price_per_head=25000.0,
            cow_price_per_head=18000.0,
            carcass_price_per_kg=55.0,
            manure_price_per_ton=150.0,
            hide_price_per_piece=200.0,
            # Performans oranları
            conception_rate=0.85,
            calving_rate=0.90,
            calf_survival_rate=0.95,
            weaning_rate=0.92,
            mortality_rate_adult=0.02,
            mortality_rate_young=0.05,
            culling_rate=0.15,
            replacement_rate=0.20,
            # Risk faktörleri
            disease_outbreak_probability=0.10,
            market_volatility_coefficient=0.15,
            weather_risk_probability=0.20,
            feed_price_volatility=0.12,
            drought_probability=0.08,
            flood_probability=0.03,
            policy_change_risk=0.05,
            # Simülasyon parametreleri
            simulation_duration_years=5,
            time_step_days=1,
            monte_carlo_iterations=1000,
            confidence_interval=0.95,
            discount_rate=0.08,
            inflation_rate=0.06,
            tax_rate=0.20
        )
        db.add(farm)
        db.flush()

        # Yemler oluştur - Feed modelindeki tüm gerekli alanları dahil et
        feeds_data = [
            {
                'name': 'Süt İneği Konsantresi', 'feed_type': 'concentrate', 'cost_per_kg': 8.50,
                'storage_life_days': 180, 'moisture_content_percentage': 12.0,
                'dry_matter_percentage': 88.0, 'crude_protein_percentage': 18.0,
                'metabolizable_energy_mcal_kg': 2.8, 'net_energy_maintenance_mcal_kg': 1.8,
                'net_energy_gain_mcal_kg': 1.2, 'crude_fiber_percentage': 8.0,
                'acid_detergent_fiber_percentage': 12.0, 'neutral_detergent_fiber_percentage': 25.0,
                'total_digestible_nutrients_percentage': 75.0, 'calcium_percentage': 0.8,
                'phosphorus_percentage': 0.6, 'rumen_degradable_protein_percentage': 65.0,
                'rumen_undegradable_protein_percentage': 35.0, 'availability_seasonal': {}
            },
            {
                'name': 'Mısır Silajı', 'feed_type': 'silage', 'cost_per_kg': 1.20,
                'storage_life_days': 365, 'moisture_content_percentage': 65.0,
                'dry_matter_percentage': 35.0, 'crude_protein_percentage': 8.0,
                'metabolizable_energy_mcal_kg': 2.4, 'net_energy_maintenance_mcal_kg': 1.5,
                'net_energy_gain_mcal_kg': 0.9, 'crude_fiber_percentage': 22.0,
                'acid_detergent_fiber_percentage': 25.0, 'neutral_detergent_fiber_percentage': 45.0,
                'total_digestible_nutrients_percentage': 68.0, 'calcium_percentage': 0.3,
                'phosphorus_percentage': 0.25, 'rumen_degradable_protein_percentage': 70.0,
                'rumen_undegradable_protein_percentage': 30.0, 'availability_seasonal': {}
            },
            {
                'name': 'Yonca Kuru Otu', 'feed_type': 'hay', 'cost_per_kg': 2.80,
                'storage_life_days': 730, 'moisture_content_percentage': 12.0,
                'dry_matter_percentage': 88.0, 'crude_protein_percentage': 18.0,
                'metabolizable_energy_mcal_kg': 2.2, 'net_energy_maintenance_mcal_kg': 1.4,
                'net_energy_gain_mcal_kg': 0.8, 'crude_fiber_percentage': 28.0,
                'acid_detergent_fiber_percentage': 32.0, 'neutral_detergent_fiber_percentage': 42.0,
                'total_digestible_nutrients_percentage': 62.0, 'calcium_percentage': 1.2,
                'phosphorus_percentage': 0.3, 'rumen_degradable_protein_percentage': 75.0,
                'rumen_undegradable_protein_percentage': 25.0, 'availability_seasonal': {}
            }
        ]

        for feed_data in feeds_data:
            feed = Feed(farm_id=farm.id, **feed_data)
            db.add(feed)

        # Hayvanlar oluştur
        breeds = ['holstein', 'angus', 'simmental']
        for i in range(30):
            breed = random.choice(breeds)
            gender = random.choice(['male', 'female'])
            age_months = random.randint(12, 60)
            birth_date = (datetime.now() - timedelta(days=age_months * 30.44)).date()

            # Irk özelliklerine göre ağırlık
            if breed == 'holstein':
                base_weight = 650 if gender == 'female' else 900
            elif breed == 'angus':
                base_weight = 550 if gender == 'female' else 850
            else:  # simmental
                base_weight = 750 if gender == 'female' else 1100

            # Yaşa göre ağırlık ayarlama
            age_factor = min(age_months / 24, 1.0)
            current_weight = base_weight * (0.3 + 0.7 * age_factor) + random.uniform(-50, 50)
            current_weight = max(current_weight, 50)

            # Durum belirleme
            if age_months < 12:
                status = 'calf'
            elif gender == 'female' and breed == 'holstein':
                status = random.choice(['breeding', 'milking', 'dry'])
            else:
                status = random.choice(['breeding', 'fattening'])

            # Gebelik durumu (sadece dişiler için)
            is_pregnant = False
            pregnancy_start_date = None
            expected_calving_date = None

            if gender == 'female' and age_months > 15 and random.random() < 0.3:  # %30 gebelik oranı
                is_pregnant = True
                pregnancy_days = random.randint(50, 250)  # Gebelik süresi
                pregnancy_start_date = (datetime.now() - timedelta(days=pregnancy_days)).date()
                expected_calving_date = (datetime.now() + timedelta(days=283 - pregnancy_days)).date()

            animal = Animal(
                farm_id=farm.id,
                breed=breed,
                birth_date=birth_date,
                gender=gender,
                current_weight_kg=round(current_weight, 1),
                body_condition_score=round(random.uniform(2.5, 4.5), 1),
                status=status,
                is_pregnant=is_pregnant,
                pregnancy_start_date=pregnancy_start_date,
                expected_calving_date=expected_calving_date,
                purchase_price=random.uniform(8000, 25000) if random.random() < 0.7 else None,
                purchase_date=(datetime.now() - timedelta(days=random.randint(30, 1000))).date() if random.random() < 0.7 else None,
                vaccination_records=[],
                health_issues=[],
                total_feed_consumed_kg=random.uniform(1000, 5000),
                total_health_costs=random.uniform(200, 1500),
                average_daily_gain_actual=random.uniform(0.8, 1.5) if random.random() < 0.8 else None,
                feed_conversion_actual=random.uniform(6.0, 10.0) if random.random() < 0.8 else None
            )
            db.add(animal)

        db.commit()
        print("✅ Sahte veri oluşturma tamamlandı!")
        print("📊 1 çiftlik, 6 yem türü, 30 hayvan oluşturuldu")

    except Exception as e:
        db.rollback()
        print(f"✗ Hata: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    main()
