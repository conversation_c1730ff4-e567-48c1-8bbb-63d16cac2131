import axios from 'axios';
import { Farm, FarmCreate, Animal, AnimalCreate, AnimalStats, Feed } from '@/types';

const API_BASE_URL = 'http://localhost:8000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Farm API
export const farmApi = {
  // Tüm çiftlikleri getir
  getFarms: async (): Promise<Farm[]> => {
    const response = await api.get('/farms/');
    return response.data;
  },

  // Belirli bir çiftliği getir
  getFarm: async (farmId: string): Promise<Farm> => {
    const response = await api.get(`/farms/${farmId}`);
    return response.data;
  },

  // Yeni çiftlik oluştur
  createFarm: async (farmData: FarmCreate): Promise<{ id: string; message: string }> => {
    const response = await api.post('/farms/', farmData);
    return response.data;
  },

  // Çiftlik güncelle
  updateFarm: async (farmId: string, farmData: FarmCreate): Promise<{ message: string }> => {
    const response = await api.put(`/farms/${farmId}`, farmData);
    return response.data;
  },

  // Çiftlik sil
  deleteFarm: async (farmId: string): Promise<{ message: string }> => {
    const response = await api.delete(`/farms/${farmId}`);
    return response.data;
  },
};

// Animal API
export const animalApi = {
  // Çiftlikteki hayvanları getir
  getAnimalsByFarm: async (farmId: string): Promise<Animal[]> => {
    const response = await api.get(`/animals/farm/${farmId}`);
    return response.data;
  },

  // Belirli bir hayvanı getir
  getAnimal: async (animalId: string): Promise<Animal> => {
    const response = await api.get(`/animals/${animalId}`);
    return response.data;
  },

  // Yeni hayvan ekle
  createAnimal: async (animalData: AnimalCreate): Promise<{ id: string; message: string }> => {
    const response = await api.post('/animals/', animalData);
    return response.data;
  },

  // Hayvan güncelle
  updateAnimal: async (animalId: string, animalData: AnimalCreate): Promise<{ message: string }> => {
    const response = await api.put(`/animals/${animalId}`, animalData);
    return response.data;
  },

  // Hayvan sil
  deleteAnimal: async (animalId: string): Promise<{ message: string }> => {
    const response = await api.delete(`/animals/${animalId}`);
    return response.data;
  },

  // Çiftlik hayvan istatistikleri
  getFarmAnimalStats: async (farmId: string): Promise<AnimalStats> => {
    const response = await api.get(`/animals/farm/${farmId}/stats`);
    return response.data;
  },
};

// Feed API
export const feedApi = {
  // Çiftlikteki yemleri getir
  getFeedsByFarm: async (farmId: string): Promise<Feed[]> => {
    const response = await api.get(`/feeds/farm/${farmId}`);
    return response.data;
  },

  // Belirli bir yemi getir
  getFeed: async (feedId: string): Promise<Feed> => {
    const response = await api.get(`/feeds/${feedId}`);
    return response.data;
  },

  // Yeni yem ekle
  createFeed: async (feedData: any): Promise<{ id: string; message: string }> => {
    const response = await api.post('/feeds/', feedData);
    return response.data;
  },

  // Yem güncelle
  updateFeed: async (feedId: string, feedData: any): Promise<{ message: string }> => {
    const response = await api.put(`/feeds/${feedId}`, feedData);
    return response.data;
  },

  // Yem sil
  deleteFeed: async (feedId: string): Promise<{ message: string }> => {
    const response = await api.delete(`/feeds/${feedId}`);
    return response.data;
  },

  // Yem türlerini getir
  getFeedTypesByFarm: async (farmId: string): Promise<Record<string, any[]>> => {
    const response = await api.get(`/feeds/farm/${farmId}/types`);
    return response.data;
  },

  // Örnek yem verileri ekle
  addSampleFeeds: async (farmId: string): Promise<{ message: string; feeds: string[] }> => {
    const response = await api.post(`/feeds/farm/${farmId}/sample-feeds`);
    return response.data;
  },
};

// Ration API
export const rationApi = {
  // Besin ihtiyaçlarını hesapla
  calculateRequirements: async (animalId: string): Promise<any> => {
    const response = await api.post(`/rations/calculate-requirements?animal_id=${animalId}`);
    return response.data;
  },

  // Rasyon optimizasyonu yap
  optimizeRation: async (request: any): Promise<any> => {
    const response = await api.post('/rations/optimize', request);
    return response.data;
  },

  // Çiftlikteki rasyonları getir
  getRationsByFarm: async (farmId: string): Promise<any[]> => {
    const response = await api.get(`/rations/farm/${farmId}`);
    return response.data;
  },

  // Rasyon detaylarını getir
  getRationDetails: async (rationId: string): Promise<any> => {
    const response = await api.get(`/rations/${rationId}`);
    return response.data;
  },

  // Rasyonu aktif hale getir
  activateRation: async (rationId: string): Promise<{ message: string }> => {
    const response = await api.put(`/rations/${rationId}/activate`);
    return response.data;
  },

  // Rasyonu sil
  deleteRation: async (rationId: string): Promise<{ message: string }> => {
    const response = await api.delete(`/rations/${rationId}`);
    return response.data;
  },

  // Hayvan için aktif rasyonu getir
  getActiveRationForAnimal: async (animalId: string): Promise<any> => {
    const response = await api.get(`/rations/animal/${animalId}/active`);
    return response.data;
  },
};

// Health check
export const healthApi = {
  checkHealth: async (): Promise<{ status: string }> => {
    const response = await api.get('/health', { baseURL: 'http://localhost:8000' });
    return response.data;
  },
};

export default api;
