{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Farm, FarmCreate, Animal, AnimalCreate, AnimalStats, Feed } from '@/types';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Farm API\nexport const farmApi = {\n  // Tüm çiftlikleri getir\n  getFarms: async (): Promise<Farm[]> => {\n    const response = await api.get('/farms/');\n    return response.data;\n  },\n\n  // Belirli bir çiftliği getir\n  getFarm: async (farmId: string): Promise<Farm> => {\n    const response = await api.get(`/farms/${farmId}`);\n    return response.data;\n  },\n\n  // Yeni çiftlik oluştur\n  createFarm: async (farmData: FarmCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/farms/', farmData);\n    return response.data;\n  },\n\n  // Çiftlik güncelle\n  updateFarm: async (farmId: string, farmData: FarmCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/farms/${farmId}`, farmData);\n    return response.data;\n  },\n\n  // Çiftlik sil\n  deleteFarm: async (farmId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/farms/${farmId}`);\n    return response.data;\n  },\n};\n\n// Animal API\nexport const animalApi = {\n  // Çiftlikteki hayvanları getir\n  getAnimalsByFarm: async (farmId: string): Promise<Animal[]> => {\n    const response = await api.get(`/animals/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir hayvanı getir\n  getAnimal: async (animalId: string): Promise<Animal> => {\n    const response = await api.get(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Yeni hayvan ekle\n  createAnimal: async (animalData: AnimalCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/animals/', animalData);\n    return response.data;\n  },\n\n  // Hayvan güncelle\n  updateAnimal: async (animalId: string, animalData: AnimalCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/animals/${animalId}`, animalData);\n    return response.data;\n  },\n\n  // Hayvan sil\n  deleteAnimal: async (animalId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftlik hayvan istatistikleri\n  getFarmAnimalStats: async (farmId: string): Promise<AnimalStats> => {\n    const response = await api.get(`/animals/farm/${farmId}/stats`);\n    return response.data;\n  },\n};\n\n// Feed API\nexport const feedApi = {\n  // Çiftlikteki yemleri getir\n  getFeedsByFarm: async (farmId: string): Promise<Feed[]> => {\n    const response = await api.get(`/feeds/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir yemi getir\n  getFeed: async (feedId: string): Promise<Feed> => {\n    const response = await api.get(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yeni yem ekle\n  createFeed: async (feedData: any): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/feeds/', feedData);\n    return response.data;\n  },\n\n  // Yem güncelle\n  updateFeed: async (feedId: string, feedData: any): Promise<{ message: string }> => {\n    const response = await api.put(`/feeds/${feedId}`, feedData);\n    return response.data;\n  },\n\n  // Yem sil\n  deleteFeed: async (feedId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yem türlerini getir\n  getFeedTypesByFarm: async (farmId: string): Promise<Record<string, any[]>> => {\n    const response = await api.get(`/feeds/farm/${farmId}/types`);\n    return response.data;\n  },\n\n  // Örnek yem verileri ekle\n  addSampleFeeds: async (farmId: string): Promise<{ message: string; feeds: string[] }> => {\n    const response = await api.post(`/feeds/farm/${farmId}/sample-feeds`);\n    return response.data;\n  },\n};\n\n// Ration API\nexport const rationApi = {\n  // Besin ihtiyaçlarını hesapla\n  calculateRequirements: async (animalId: string): Promise<any> => {\n    const response = await api.post(`/rations/calculate-requirements?animal_id=${animalId}`);\n    return response.data;\n  },\n\n  // Rasyon optimizasyonu yap\n  optimizeRation: async (request: any): Promise<any> => {\n    const response = await api.post('/rations/optimize', request);\n    return response.data;\n  },\n\n  // Çiftlikteki rasyonları getir\n  getRationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/rations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Rasyon detaylarını getir\n  getRationDetails: async (rationId: string): Promise<any> => {\n    const response = await api.get(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Rasyonu aktif hale getir\n  activateRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.put(`/rations/${rationId}/activate`);\n    return response.data;\n  },\n\n  // Rasyonu sil\n  deleteRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Hayvan için aktif rasyonu getir\n  getActiveRationForAnimal: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/rations/animal/${animalId}/active`);\n    return response.data;\n  },\n};\n\n// Simulation API\nexport const simulationApi = {\n  // Simülasyon çalıştır\n  runSimulation: async (request: any): Promise<any> => {\n    const response = await api.post('/simulations/run', request);\n    return response.data;\n  },\n\n  // Çiftlikteki simülasyonları getir\n  getSimulationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/simulations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Simülasyon detaylarını getir\n  getSimulationDetails: async (simulationId: string): Promise<any> => {\n    const response = await api.get(`/simulations/${simulationId}`);\n    return response.data;\n  },\n\n  // Simülasyonu sil\n  deleteSimulation: async (simulationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/simulations/${simulationId}`);\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthApi = {\n  checkHealth: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health', { baseURL: 'http://localhost:8000' });\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAGA,MAAM,eAAe;AAErB,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,UAAU;IACrB,wBAAwB;IACxB,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,cAAc,OAAO,UAAkB;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;QACpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,8BAA8B;IAC9B,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,0CAA0C,EAAE,UAAU;QACvF,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,kCAAkC;IAClC,0BAA0B,OAAO;QAC/B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC;QACnE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,gBAAgB;IAC3B,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,mCAAmC;IACnC,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,aAAa,EAAE,cAAc;QAChE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,WAAW;YAAE,SAAS;QAAwB;QAC7E,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/app/simulation/create/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { \n  ArrowLeft, \n  TrendingUp, \n  AlertCircle, \n  CheckCircle,\n  Calculator,\n  Clock,\n  Target,\n  Users\n} from 'lucide-react';\nimport { simulationApi, farmApi, animalApi } from '@/services/api';\nimport { Farm, Animal, SimulationResult } from '@/types';\n\nexport default function CreateSimulationPage() {\n  const router = useRouter();\n  const [farms, setFarms] = useState<Farm[]>([]);\n  const [animals, setAnimals] = useState<Animal[]>([]);\n  const [selectedFarmId, setSelectedFarmId] = useState<string>('');\n  const [selectedAnimalId, setSelectedAnimalId] = useState<string>('');\n  const [simulationName, setSimulationName] = useState<string>('');\n  const [endAgeMonths, setEndAgeMonths] = useState<number>(60);\n  const [slaughterAgeMonths, setSlaughterAgeMonths] = useState<number | undefined>();\n  const [useSlaughterAge, setUseSlaughterAge] = useState<boolean>(false);\n  const [result, setResult] = useState<SimulationResult | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string>('');\n\n  useEffect(() => {\n    loadFarms();\n  }, []);\n\n  useEffect(() => {\n    if (selectedFarmId) {\n      loadAnimals();\n    }\n  }, [selectedFarmId]);\n\n  useEffect(() => {\n    if (selectedAnimalId) {\n      generateSimulationName();\n    }\n  }, [selectedAnimalId]);\n\n  const loadFarms = async () => {\n    try {\n      const farmsData = await farmApi.getFarms();\n      setFarms(farmsData);\n      if (farmsData.length > 0) {\n        setSelectedFarmId(farmsData[0].id);\n      }\n    } catch (err) {\n      setError('Çiftlikler yüklenirken hata oluştu');\n      console.error(err);\n    }\n  };\n\n  const loadAnimals = async () => {\n    if (!selectedFarmId) return;\n    \n    try {\n      const animalsData = await animalApi.getAnimalsByFarm(selectedFarmId);\n      setAnimals(animalsData);\n      setSelectedAnimalId('');\n      setResult(null);\n    } catch (err) {\n      setError('Hayvanlar yüklenirken hata oluştu');\n      console.error(err);\n    }\n  };\n\n  const generateSimulationName = () => {\n    const selectedAnimal = animals.find(a => a.id === selectedAnimalId);\n    if (selectedAnimal) {\n      const currentDate = new Date().toLocaleDateString('tr-TR');\n      setSimulationName(`${selectedAnimal.breed} Yaşam Boyu Analizi - ${currentDate}`);\n    }\n  };\n\n  const calculateCurrentAge = (birthDate: string): number => {\n    const birth = new Date(birthDate);\n    const now = new Date();\n    const diffTime = Math.abs(now.getTime() - birth.getTime());\n    const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30.44));\n    return diffMonths;\n  };\n\n  const handleCreateSimulation = async () => {\n    if (!selectedFarmId || !selectedAnimalId || !simulationName.trim()) {\n      setError('Lütfen tüm gerekli alanları doldurun');\n      return;\n    }\n\n    const selectedAnimal = animals.find(a => a.id === selectedAnimalId);\n    if (!selectedAnimal) {\n      setError('Seçilen hayvan bulunamadı');\n      return;\n    }\n\n    const currentAge = calculateCurrentAge(selectedAnimal.birth_date);\n    if (endAgeMonths <= currentAge) {\n      setError('Bitiş yaşı mevcut yaştan büyük olmalıdır');\n      return;\n    }\n\n    if (useSlaughterAge && slaughterAgeMonths && slaughterAgeMonths <= currentAge) {\n      setError('Kesim yaşı mevcut yaştan büyük olmalıdır');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      const requestData = {\n        animal_id: selectedAnimalId,\n        farm_id: selectedFarmId,\n        name: simulationName.trim(),\n        end_age_months: endAgeMonths,\n        slaughter_age_months: useSlaughterAge ? slaughterAgeMonths : undefined\n      };\n\n      const simulationResult = await simulationApi.runSimulation(requestData);\n      setResult(simulationResult);\n    } catch (err: any) {\n      setError(err.response?.data?.detail || 'Simülasyon oluşturulurken hata oluştu');\n      console.error(err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getSimulationDuration = () => {\n    if (!selectedAnimalId) return '';\n    \n    const selectedAnimal = animals.find(a => a.id === selectedAnimalId);\n    if (!selectedAnimal) return '';\n    \n    const currentAge = calculateCurrentAge(selectedAnimal.birth_date);\n    const targetAge = useSlaughterAge && slaughterAgeMonths ? slaughterAgeMonths : endAgeMonths;\n    const months = targetAge - currentAge;\n    const years = Math.floor(months / 12);\n    const remainingMonths = months % 12;\n    \n    if (years > 0) {\n      return `${years} yıl ${remainingMonths > 0 ? `${remainingMonths} ay` : ''}`;\n    }\n    return `${months} ay`;\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4\">\n        <button\n          onClick={() => router.back()}\n          className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n        >\n          <ArrowLeft className=\"h-5 w-5 text-gray-600\" />\n        </button>\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Yeni Simülasyon Oluştur</h1>\n          <p className=\"text-gray-600 mt-2\">\n            Hayvan için yaşam boyu yem tüketimi ve maliyet analizi\n          </p>\n        </div>\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n          <div className=\"flex\">\n            <AlertCircle className=\"h-5 w-5 text-red-400\" />\n            <div className=\"ml-3\">\n              <p className=\"text-sm text-red-800\">{error}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Sol Kolon - Form */}\n        <div className=\"space-y-6\">\n          {/* Çiftlik ve Hayvan Seçimi */}\n          <div className=\"content-overlay p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              Hayvan Seçimi\n            </h2>\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Çiftlik\n                </label>\n                <select\n                  value={selectedFarmId}\n                  onChange={(e) => setSelectedFarmId(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                >\n                  <option value=\"\">Çiftlik seçin</option>\n                  {farms.map((farm) => (\n                    <option key={farm.id} value={farm.id}>\n                      {farm.name} - {farm.location}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Hayvan\n                </label>\n                <select\n                  value={selectedAnimalId}\n                  onChange={(e) => setSelectedAnimalId(e.target.value)}\n                  disabled={!selectedFarmId || animals.length === 0}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 disabled:bg-gray-100\"\n                >\n                  <option value=\"\">Hayvan seçin</option>\n                  {animals.map((animal) => (\n                    <option key={animal.id} value={animal.id}>\n                      {animal.breed} - {animal.current_weight_kg}kg - {calculateCurrentAge(animal.birth_date)} ay\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Simülasyon Parametreleri */}\n          <div className=\"content-overlay p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              Simülasyon Parametreleri\n            </h2>\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Simülasyon Adı *\n                </label>\n                <input\n                  type=\"text\"\n                  value={simulationName}\n                  onChange={(e) => setSimulationName(e.target.value)}\n                  placeholder=\"Simülasyon adı girin\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Maksimum Yaş (ay) *\n                </label>\n                <input\n                  type=\"number\"\n                  value={endAgeMonths}\n                  onChange={(e) => setEndAgeMonths(parseInt(e.target.value) || 60)}\n                  min=\"12\"\n                  max=\"180\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  Simülasyonun kaç aylık yaşa kadar çalışacağını belirtin (12-180 ay)\n                </p>\n              </div>\n\n              <div>\n                <div className=\"flex items-center space-x-2 mb-2\">\n                  <input\n                    type=\"checkbox\"\n                    id=\"useSlaughterAge\"\n                    checked={useSlaughterAge}\n                    onChange={(e) => setUseSlaughterAge(e.target.checked)}\n                    className=\"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                  />\n                  <label htmlFor=\"useSlaughterAge\" className=\"text-sm font-medium text-gray-700\">\n                    Kesim yaşı belirle\n                  </label>\n                </div>\n                \n                {useSlaughterAge && (\n                  <input\n                    type=\"number\"\n                    value={slaughterAgeMonths || ''}\n                    onChange={(e) => setSlaughterAgeMonths(parseInt(e.target.value) || undefined)}\n                    placeholder=\"Kesim yaşı (ay)\"\n                    min=\"6\"\n                    max={endAgeMonths}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                  />\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Simülasyon Oluştur Butonu */}\n          <button\n            onClick={handleCreateSimulation}\n            disabled={!selectedAnimalId || !simulationName.trim() || loading}\n            className=\"w-full btn-primary text-white py-3 rounded-lg flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {loading ? (\n              <>\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\n                <span>Simülasyon Oluşturuluyor...</span>\n              </>\n            ) : (\n              <>\n                <TrendingUp className=\"h-5 w-5\" />\n                <span>Simülasyon Oluştur</span>\n              </>\n            )}\n          </button>\n        </div>\n\n        {/* Sağ Kolon - Önizleme ve Sonuç */}\n        <div className=\"space-y-6\">\n          {/* Simülasyon Önizlemesi */}\n          {selectedAnimalId && (\n            <div className=\"content-overlay p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                Simülasyon Önizlemesi\n              </h2>\n              \n              {(() => {\n                const selectedAnimal = animals.find(a => a.id === selectedAnimalId);\n                if (!selectedAnimal) return null;\n                \n                const currentAge = calculateCurrentAge(selectedAnimal.birth_date);\n                \n                return (\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      <Users className=\"h-4 w-4 text-gray-500\" />\n                      <span className=\"text-sm text-gray-600\">Hayvan:</span>\n                      <span className=\"font-medium text-gray-900\">\n                        {selectedAnimal.breed} - {selectedAnimal.gender === 'male' ? 'Erkek' : 'Dişi'}\n                      </span>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-2\">\n                      <Target className=\"h-4 w-4 text-gray-500\" />\n                      <span className=\"text-sm text-gray-600\">Mevcut Yaş:</span>\n                      <span className=\"font-medium text-gray-900\">{currentAge} ay</span>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-2\">\n                      <Calculator className=\"h-4 w-4 text-gray-500\" />\n                      <span className=\"text-sm text-gray-600\">Mevcut Ağırlık:</span>\n                      <span className=\"font-medium text-gray-900\">{selectedAnimal.current_weight_kg} kg</span>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-2\">\n                      <Clock className=\"h-4 w-4 text-gray-500\" />\n                      <span className=\"text-sm text-gray-600\">Simülasyon Süresi:</span>\n                      <span className=\"font-medium text-gray-900\">{getSimulationDuration()}</span>\n                    </div>\n                  </div>\n                );\n              })()}\n            </div>\n          )}\n\n          {/* Simülasyon Sonucu */}\n          {result && (\n            <div className=\"content-overlay p-6\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <CheckCircle className=\"h-5 w-5 text-green-500\" />\n                <h2 className=\"text-lg font-semibold text-gray-900\">\n                  Simülasyon Tamamlandı\n                </h2>\n              </div>\n              \n              <div className=\"space-y-4\">\n                <div className=\"bg-green-50 p-4 rounded-lg\">\n                  <p className=\"text-green-800 font-medium mb-2\">{result.message}</p>\n                  <p className=\"text-sm text-green-700\">\n                    Simülasyon ID: {result.simulation_id}\n                  </p>\n                </div>\n\n                <div className=\"flex space-x-3\">\n                  <button\n                    onClick={() => router.push(`/simulation/${result.simulation_id}`)}\n                    className=\"flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                  >\n                    Detayları Görüntüle\n                  </button>\n                  \n                  <button\n                    onClick={() => router.push('/simulation')}\n                    className=\"flex-1 bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700 transition-colors\"\n                  >\n                    Simülasyonlar\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAdA;;;;;;AAiBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB;QACF;IACF,GAAG;QAAC;KAAe;IAEnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB;YACpB;QACF;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,YAAY,MAAM,sHAAA,CAAA,UAAO,CAAC,QAAQ;YACxC,SAAS;YACT,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,kBAAkB,SAAS,CAAC,EAAE,CAAC,EAAE;YACnC;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,cAAc,MAAM,sHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;YACrD,WAAW;YACX,oBAAoB;YACpB,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,yBAAyB;QAC7B,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAClD,IAAI,gBAAgB;YAClB,MAAM,cAAc,IAAI,OAAO,kBAAkB,CAAC;YAClD,kBAAkB,GAAG,eAAe,KAAK,CAAC,sBAAsB,EAAE,aAAa;QACjF;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,QAAQ,IAAI,KAAK;QACvB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO;QACvD,MAAM,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,KAAK,KAAK;QACpE,OAAO;IACT;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,eAAe,IAAI,IAAI;YAClE,SAAS;YACT;QACF;QAEA,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAClD,IAAI,CAAC,gBAAgB;YACnB,SAAS;YACT;QACF;QAEA,MAAM,aAAa,oBAAoB,eAAe,UAAU;QAChE,IAAI,gBAAgB,YAAY;YAC9B,SAAS;YACT;QACF;QAEA,IAAI,mBAAmB,sBAAsB,sBAAsB,YAAY;YAC7E,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,cAAc;gBAClB,WAAW;gBACX,SAAS;gBACT,MAAM,eAAe,IAAI;gBACzB,gBAAgB;gBAChB,sBAAsB,kBAAkB,qBAAqB;YAC/D;YAEA,MAAM,mBAAmB,MAAM,sHAAA,CAAA,gBAAa,CAAC,aAAa,CAAC;YAC3D,UAAU;QACZ,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,UAAU;YACvC,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,kBAAkB,OAAO;QAE9B,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAClD,IAAI,CAAC,gBAAgB,OAAO;QAE5B,MAAM,aAAa,oBAAoB,eAAe,UAAU;QAChE,MAAM,YAAY,mBAAmB,qBAAqB,qBAAqB;QAC/E,MAAM,SAAS,YAAY;QAC3B,MAAM,QAAQ,KAAK,KAAK,CAAC,SAAS;QAClC,MAAM,kBAAkB,SAAS;QAEjC,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,KAAK,EAAE,kBAAkB,IAAI,GAAG,gBAAgB,GAAG,CAAC,GAAG,IAAI;QAC7E;QACA,OAAO,GAAG,OAAO,GAAG,CAAC;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI;wBAC1B,WAAU;kCAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;YAOrC,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAM7C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAIzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;oEAAqB,OAAO,KAAK,EAAE;;wEACjC,KAAK,IAAI;wEAAC;wEAAI,KAAK,QAAQ;;mEADjB,KAAK,EAAE;;;;;;;;;;;;;;;;;0DAO1B,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wDACnD,UAAU,CAAC,kBAAkB,QAAQ,MAAM,KAAK;wDAChD,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oEAAuB,OAAO,OAAO,EAAE;;wEACrC,OAAO,KAAK;wEAAC;wEAAI,OAAO,iBAAiB;wEAAC;wEAAM,oBAAoB,OAAO,UAAU;wEAAE;;mEAD7E,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAIzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAId,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,gBAAgB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wDAC7D,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;kEAEZ,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAK5C,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,SAAS;gEACT,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,OAAO;gEACpD,WAAU;;;;;;0EAEZ,8OAAC;gEAAM,SAAQ;gEAAkB,WAAU;0EAAoC;;;;;;;;;;;;oDAKhF,iCACC,8OAAC;wDACC,MAAK;wDACL,OAAO,sBAAsB;wDAC7B,UAAU,CAAC,IAAM,sBAAsB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wDACnE,aAAY;wDACZ,KAAI;wDACJ,KAAK;wDACL,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAQpB,8OAAC;gCACC,SAAS;gCACT,UAAU,CAAC,oBAAoB,CAAC,eAAe,IAAI,MAAM;gCACzD,WAAU;0CAET,wBACC;;sDACE,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;iEAGR;;sDACE,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;kCAOd,8OAAC;wBAAI,WAAU;;4BAEZ,kCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;oCAIxD,CAAC;wCACA,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;wCAClD,IAAI,CAAC,gBAAgB,OAAO;wCAE5B,MAAM,aAAa,oBAAoB,eAAe,UAAU;wCAEhE,qBACE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;;gEACb,eAAe,KAAK;gEAAC;gEAAI,eAAe,MAAM,KAAK,SAAS,UAAU;;;;;;;;;;;;;8DAI3E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;;gEAA6B;gEAAW;;;;;;;;;;;;;8DAG1D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8MAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;;gEAA6B,eAAe,iBAAiB;gEAAC;;;;;;;;;;;;;8DAGhF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;oCAIrD,CAAC;;;;;;;4BAKJ,wBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;;kDAKtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAmC,OAAO,OAAO;;;;;;kEAC9D,8OAAC;wDAAE,WAAU;;4DAAyB;4DACpB,OAAO,aAAa;;;;;;;;;;;;;0DAIxC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,EAAE;wDAChE,WAAU;kEACX;;;;;;kEAID,8OAAC;wDACC,SAAS,IAAM,OAAO,IAAI,CAAC;wDAC3B,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}]}