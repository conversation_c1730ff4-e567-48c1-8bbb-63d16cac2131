{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Farm, FarmCreate, Animal, AnimalCreate, AnimalStats, Feed } from '@/types';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Farm API\nexport const farmApi = {\n  // Tüm çiftlikleri getir\n  getFarms: async (): Promise<Farm[]> => {\n    const response = await api.get('/farms/');\n    return response.data;\n  },\n\n  // Belirli bir çiftliği getir\n  getFarm: async (farmId: string): Promise<Farm> => {\n    const response = await api.get(`/farms/${farmId}`);\n    return response.data;\n  },\n\n  // Yeni çiftlik oluştur\n  createFarm: async (farmData: FarmCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/farms/', farmData);\n    return response.data;\n  },\n\n  // Çiftlik güncelle\n  updateFarm: async (farmId: string, farmData: FarmCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/farms/${farmId}`, farmData);\n    return response.data;\n  },\n\n  // Çiftlik sil\n  deleteFarm: async (farmId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/farms/${farmId}`);\n    return response.data;\n  },\n};\n\n// Animal API\nexport const animalApi = {\n  // Çiftlikteki hayvanları getir\n  getAnimalsByFarm: async (farmId: string): Promise<Animal[]> => {\n    const response = await api.get(`/animals/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir hayvanı getir\n  getAnimal: async (animalId: string): Promise<Animal> => {\n    const response = await api.get(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Yeni hayvan ekle\n  createAnimal: async (animalData: AnimalCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/animals/', animalData);\n    return response.data;\n  },\n\n  // Hayvan güncelle\n  updateAnimal: async (animalId: string, animalData: AnimalCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/animals/${animalId}`, animalData);\n    return response.data;\n  },\n\n  // Hayvan sil\n  deleteAnimal: async (animalId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftlik hayvan istatistikleri\n  getFarmAnimalStats: async (farmId: string): Promise<AnimalStats> => {\n    const response = await api.get(`/animals/farm/${farmId}/stats`);\n    return response.data;\n  },\n};\n\n// Feed API\nexport const feedApi = {\n  // Çiftlikteki yemleri getir\n  getFeedsByFarm: async (farmId: string): Promise<Feed[]> => {\n    const response = await api.get(`/feeds/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir yemi getir\n  getFeed: async (feedId: string): Promise<Feed> => {\n    const response = await api.get(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yeni yem ekle\n  createFeed: async (feedData: any): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/feeds/', feedData);\n    return response.data;\n  },\n\n  // Yem güncelle\n  updateFeed: async (feedId: string, feedData: any): Promise<{ message: string }> => {\n    const response = await api.put(`/feeds/${feedId}`, feedData);\n    return response.data;\n  },\n\n  // Yem sil\n  deleteFeed: async (feedId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yem türlerini getir\n  getFeedTypesByFarm: async (farmId: string): Promise<Record<string, any[]>> => {\n    const response = await api.get(`/feeds/farm/${farmId}/types`);\n    return response.data;\n  },\n\n  // Örnek yem verileri ekle\n  addSampleFeeds: async (farmId: string): Promise<{ message: string; feeds: string[] }> => {\n    const response = await api.post(`/feeds/farm/${farmId}/sample-feeds`);\n    return response.data;\n  },\n};\n\n// Ration API\nexport const rationApi = {\n  // Besin ihtiyaçlarını hesapla\n  calculateRequirements: async (animalId: string): Promise<any> => {\n    const response = await api.post(`/rations/calculate-requirements?animal_id=${animalId}`);\n    return response.data;\n  },\n\n  // Rasyon optimizasyonu yap\n  optimizeRation: async (request: any): Promise<any> => {\n    const response = await api.post('/rations/optimize', request);\n    return response.data;\n  },\n\n  // Çiftlikteki rasyonları getir\n  getRationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/rations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Rasyon detaylarını getir\n  getRationDetails: async (rationId: string): Promise<any> => {\n    const response = await api.get(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Rasyonu aktif hale getir\n  activateRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.put(`/rations/${rationId}/activate`);\n    return response.data;\n  },\n\n  // Rasyonu sil\n  deleteRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Hayvan için aktif rasyonu getir\n  getActiveRationForAnimal: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/rations/animal/${animalId}/active`);\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthApi = {\n  checkHealth: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health', { baseURL: 'http://localhost:8000' });\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAGA,MAAM,eAAe;AAErB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,UAAU;IACrB,wBAAwB;IACxB,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,cAAc,OAAO,UAAkB;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;QACpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,8BAA8B;IAC9B,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,0CAA0C,EAAE,UAAU;QACvF,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,kCAAkC;IAClC,0BAA0B,OAAO;QAC/B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC;QACnE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,WAAW;YAAE,SAAS;QAAwB;QAC7E,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/types/index.ts"], "sourcesContent": ["// Enum tanımları\nexport enum CattleBreed {\n  ANGUS = \"angus\",\n  HEREFORD = \"hereford\",\n  SIMMENTAL = \"simmental\",\n  CHAROLAIS = \"charolais\",\n  LIMOUSIN = \"limousin\",\n  HOLSTEIN = \"holstein\",\n  BROWN_SWISS = \"brown_swiss\",\n  NATIVE_ANATOLIAN = \"native_anatolian\",\n  CROSSBRED = \"crossbred\"\n}\n\nexport enum AnimalStatus {\n  CALF = \"calf\",\n  YOUNG = \"young\",\n  BREEDING = \"breeding\",\n  FATTENING = \"fattening\",\n  READY_FOR_SALE = \"ready_for_sale\",\n  SOLD = \"sold\",\n  DEAD = \"dead\"\n}\n\nexport enum FeedType {\n  CONCENTRATE = \"concentrate\",\n  ROUGHAGE = \"roughage\",\n  HAY = \"hay\",\n  SILAGE = \"silage\",\n  PASTURE = \"pasture\",\n  MINERAL_VITAMIN = \"mineral_vitamin\"\n}\n\nexport enum Gender {\n  MALE = \"male\",\n  FEMALE = \"female\"\n}\n\n// Interface tanımları\nexport interface Farm {\n  id: string;\n  name: string;\n  location: string;\n  established_date: string;\n  total_land_hectares: number;\n  pasture_land_hectares: number;\n  barn_capacity: number;\n  feed_storage_capacity_tons: number;\n  silage_capacity_tons: number;\n  hay_storage_capacity_tons: number;\n  water_storage_capacity_liters: number;\n  milking_parlor_capacity?: number;\n  quarantine_facility_capacity: number;\n  hospital_pen_capacity: number;\n  handling_facility_present: boolean;\n  scale_capacity_kg: number;\n}\n\nexport interface Animal {\n  id: string;\n  farm_id: string;\n  breed: CattleBreed;\n  birth_date: string;\n  gender: Gender;\n  current_weight_kg: number;\n  body_condition_score: number;\n  status: AnimalStatus;\n  is_pregnant: boolean;\n  age_months: number;\n  pregnancy_start_date?: string;\n  expected_calving_date?: string;\n  dam_id?: string;\n  sire_id?: string;\n  purchase_price?: number;\n  purchase_date?: string;\n}\n\nexport interface Feed {\n  id: string;\n  farm_id: string;\n  name: string;\n  feed_type: FeedType;\n  cost_per_kg: number;\n  dry_matter_percentage: number;\n  crude_protein_percentage: number;\n  metabolizable_energy_mcal_kg: number;\n  storage_life_days: number;\n  moisture_content_percentage: number;\n}\n\nexport interface FarmCreate {\n  name: string;\n  location: string;\n  total_land_hectares: number;\n  pasture_land_hectares: number;\n  barn_capacity: number;\n  feed_storage_capacity_tons: number;\n  silage_capacity_tons: number;\n  hay_storage_capacity_tons: number;\n  water_storage_capacity_liters: number;\n  milking_parlor_capacity?: number;\n  quarantine_facility_capacity: number;\n  hospital_pen_capacity: number;\n  handling_facility_present: boolean;\n  scale_capacity_kg: number;\n  labor_cost_monthly?: number;\n  electricity_cost_monthly?: number;\n  water_cost_monthly?: number;\n  fuel_cost_monthly?: number;\n  insurance_cost_monthly?: number;\n  maintenance_cost_monthly?: number;\n  veterinary_cost_annual?: number;\n  taxation_annual?: number;\n  depreciation_annual?: number;\n  interest_cost_annual?: number;\n  vaccination_cost_per_animal?: number;\n  deworming_cost_per_animal?: number;\n  hoof_care_cost_per_animal?: number;\n  breeding_cost_per_service?: number;\n  live_cattle_price_per_kg?: number;\n  calf_price_per_head?: number;\n  bull_price_per_head?: number;\n  cow_price_per_head?: number;\n  carcass_price_per_kg?: number;\n  manure_price_per_ton?: number;\n  hide_price_per_piece?: number;\n  conception_rate?: number;\n  calving_rate?: number;\n  calf_survival_rate?: number;\n  weaning_rate?: number;\n  mortality_rate_adult?: number;\n  mortality_rate_young?: number;\n  culling_rate?: number;\n  replacement_rate?: number;\n  disease_outbreak_probability?: number;\n  market_volatility_coefficient?: number;\n  weather_risk_probability?: number;\n  feed_price_volatility?: number;\n  drought_probability?: number;\n  flood_probability?: number;\n  policy_change_risk?: number;\n  discount_rate?: number;\n  inflation_rate?: number;\n  tax_rate?: number;\n}\n\nexport interface AnimalCreate {\n  farm_id: string;\n  breed: CattleBreed;\n  birth_date: string;\n  gender: Gender;\n  current_weight_kg: number;\n  body_condition_score: number;\n  status: AnimalStatus;\n  is_pregnant?: boolean;\n  pregnancy_start_date?: string;\n  expected_calving_date?: string;\n  dam_id?: string;\n  sire_id?: string;\n  purchase_price?: number;\n  purchase_date?: string;\n}\n\nexport interface AnimalStats {\n  total_animals: number;\n  by_gender: {\n    male: number;\n    female: number;\n  };\n  by_status: Record<string, number>;\n  by_breed: Record<string, number>;\n  average_weight: number;\n  pregnant_count: number;\n}\n\n// API Response types\nexport interface ApiResponse<T> {\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\n// Breed characteristics\nexport interface BreedCharacteristics {\n  breed: CattleBreed;\n  mature_weight_male_kg: number;\n  mature_weight_female_kg: number;\n  birth_weight_kg: number;\n  weaning_weight_kg: number;\n  yearling_weight_kg: number;\n  average_daily_gain_kg: number;\n  feed_conversion_ratio: number;\n  dressing_percentage: number;\n  age_at_first_calving_months: number;\n  gestation_length_days: number;\n  calving_interval_days: number;\n  longevity_years: number;\n  calving_ease_score: number;\n  milk_production_potential_kg: number;\n}\n\n// Ration types\nexport type RationType = 'individual' | 'group' | 'template';\nexport type OptimizationObjective = 'cost' | 'performance' | 'balanced';\nexport type TargetGroup = 'calves' | 'heifers' | 'lactating_cows' | 'dry_cows' | 'bulls';\n\nexport interface NutritionalRequirement {\n  animal_id: string;\n  dry_matter_intake_kg: number;\n  energy_requirements: {\n    net_energy_maintenance: number;\n    net_energy_gain: number;\n    pregnancy_energy: number;\n    lactation_energy: number;\n    total_net_energy: number;\n    metabolizable_energy: number;\n  };\n  protein_requirements: {\n    metabolizable_protein_maintenance: number;\n    metabolizable_protein_gain: number;\n    pregnancy_protein: number;\n    lactation_protein: number;\n    total_metabolizable_protein: number;\n    crude_protein: number;\n    rumen_degradable_protein: number;\n    rumen_undegradable_protein: number;\n  };\n  mineral_requirements: {\n    calcium: number;\n    phosphorus: number;\n  };\n}\n\nexport interface RationComponent {\n  id: string;\n  feed_name: string;\n  feed_type: string;\n  amount_kg_per_day: number;\n  percentage_of_total_dm: number;\n  dry_matter_contribution_kg: number;\n  protein_contribution_kg: number;\n  energy_contribution_mcal: number;\n  calcium_contribution_kg: number;\n  phosphorus_contribution_kg: number;\n  cost_contribution: number;\n}\n\nexport interface Ration {\n  id: string;\n  farm_id: string;\n  animal_id?: string;\n  name: string;\n  description?: string;\n  ration_type: RationType;\n  target_group?: TargetGroup;\n  total_cost_per_day: number;\n  total_dry_matter_kg: number;\n  total_crude_protein_percentage: number;\n  total_metabolizable_energy_mcal: number;\n  total_calcium_percentage: number;\n  total_phosphorus_percentage: number;\n  is_optimized: boolean;\n  optimization_objective?: OptimizationObjective;\n  optimization_score?: number;\n  is_active: boolean;\n  created_at: string;\n}\n\nexport interface RationDetails {\n  ration: Ration;\n  components: RationComponent[];\n}\n\nexport interface OptimizationRequest {\n  farm_id: string;\n  animal_id: string;\n  objective: OptimizationObjective;\n  name: string;\n  description?: string;\n}\n\nexport interface OptimizationResult {\n  ration_id: string;\n  message: string;\n  optimization_result: {\n    total_cost_per_day: number;\n    total_dry_matter_kg: number;\n    components: Array<{\n      feed_name: string;\n      amount_kg: number;\n      cost: number;\n    }>;\n    adequacy_ratios: {\n      protein_adequacy: number;\n      energy_adequacy: number;\n      calcium_adequacy: number;\n      phosphorus_adequacy: number;\n    };\n  };\n}\n"], "names": [], "mappings": "AAAA,iBAAiB;;;;;;;AACV,IAAA,AAAK,qCAAA;;;;;;;;;;WAAA;;AAYL,IAAA,AAAK,sCAAA;;;;;;;;WAAA;;AAUL,IAAA,AAAK,kCAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,gCAAA;;;WAAA", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/app/animals/%5Bid%5D/edit/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter, useParams } from 'next/navigation';\nimport Link from 'next/link';\nimport { ArrowLeft, Save } from 'lucide-react';\nimport { animalApi, farmApi } from '@/services/api';\nimport { AnimalCreate, Farm, CattleBreed, Gender, AnimalStatus, Animal } from '@/types';\n\nexport default function EditAnimalPage() {\n  const router = useRouter();\n  const params = useParams();\n  const animalId = params.id as string;\n  \n  const [farms, setFarms] = useState<Farm[]>([]);\n  const [animal, setAnimal] = useState<Animal | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [loadingData, setLoadingData] = useState(true);\n  const [formData, setFormData] = useState<AnimalCreate>({\n    farm_id: '',\n    breed: CattleBreed.ANGUS,\n    birth_date: '',\n    gender: Gender.FEMALE,\n    current_weight_kg: 0,\n    body_condition_score: 3.0,\n    status: AnimalStatus.CALF,\n    is_pregnant: false,\n  });\n\n  useEffect(() => {\n    loadData();\n  }, [animalId]);\n\n  const loadData = async () => {\n    try {\n      setLoadingData(true);\n      const [farmsData, animalData] = await Promise.all([\n        farmApi.getFarms(),\n        animalApi.getAnimal(animalId)\n      ]);\n      \n      setFarms(farmsData);\n      setAnimal(animalData);\n      \n      // Form verilerini doldur\n      setFormData({\n        farm_id: animalData.farm_id,\n        breed: animalData.breed as CattleBreed,\n        birth_date: animalData.birth_date.split('T')[0], // ISO date'i YYYY-MM-DD formatına çevir\n        gender: animalData.gender as Gender,\n        current_weight_kg: animalData.current_weight_kg,\n        body_condition_score: animalData.body_condition_score,\n        status: animalData.status as AnimalStatus,\n        is_pregnant: animalData.is_pregnant,\n        pregnancy_start_date: animalData.pregnancy_start_date ? animalData.pregnancy_start_date.split('T')[0] : undefined,\n        expected_calving_date: animalData.expected_calving_date ? animalData.expected_calving_date.split('T')[0] : undefined,\n        dam_id: animalData.dam_id,\n        sire_id: animalData.sire_id,\n        purchase_price: animalData.purchase_price,\n        purchase_date: animalData.purchase_date ? animalData.purchase_date.split('T')[0] : undefined,\n      });\n    } catch (err) {\n      console.error('Error loading data:', err);\n      alert('Veri yüklenirken hata oluştu');\n    } finally {\n      setLoadingData(false);\n    }\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value, type } = e.target;\n    const checked = (e.target as HTMLInputElement).checked;\n    \n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : \n               type === 'number' ? parseFloat(value) || 0 : \n               value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!formData.farm_id || !formData.birth_date) {\n      alert('Çiftlik ve doğum tarihi zorunludur');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      await animalApi.updateAnimal(animalId, formData);\n      router.push(`/animals?farm=${formData.farm_id}`);\n    } catch (err) {\n      alert('Hayvan güncellenirken hata oluştu');\n      console.error('Error updating animal:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const breedOptions = [\n    { value: CattleBreed.ANGUS, label: 'Angus' },\n    { value: CattleBreed.HEREFORD, label: 'Hereford' },\n    { value: CattleBreed.SIMMENTAL, label: 'Simmental' },\n    { value: CattleBreed.CHAROLAIS, label: 'Charolais' },\n    { value: CattleBreed.LIMOUSIN, label: 'Limousin' },\n    { value: CattleBreed.HOLSTEIN, label: 'Holstein' },\n    { value: CattleBreed.BROWN_SWISS, label: 'Brown Swiss' },\n    { value: CattleBreed.NATIVE_ANATOLIAN, label: 'Yerli Anadolu' },\n    { value: CattleBreed.CROSSBRED, label: 'Melez' },\n  ];\n\n  const statusOptions = [\n    { value: AnimalStatus.CALF, label: 'Buzağı' },\n    { value: AnimalStatus.YOUNG, label: 'Genç' },\n    { value: AnimalStatus.BREEDING, label: 'Damızlık' },\n    { value: AnimalStatus.FATTENING, label: 'Besi' },\n    { value: AnimalStatus.READY_FOR_SALE, label: 'Satışa Hazır' },\n  ];\n\n  if (loadingData) {\n    return (\n      <div className=\"flex items-center justify-center min-h-[400px]\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Hayvan bilgileri yükleniyor...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!animal) {\n    return (\n      <div className=\"text-center py-12\">\n        <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n          Hayvan bulunamadı\n        </h3>\n        <p className=\"text-gray-600 mb-6\">\n          Aradığınız hayvan mevcut değil\n        </p>\n        <Link\n          href=\"/animals\"\n          className=\"btn-primary text-white px-6 py-3 rounded-md\"\n        >\n          Hayvan Listesine Dön\n        </Link>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-2xl mx-auto space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4\">\n        <Link\n          href={`/animals?farm=${formData.farm_id}`}\n          className=\"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors\"\n        >\n          <ArrowLeft className=\"h-5 w-5\" />\n        </Link>\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Hayvan Düzenle</h1>\n          <p className=\"text-gray-600 mt-1\">\n            Hayvan bilgilerini güncelleyin\n          </p>\n        </div>\n      </div>\n\n      {/* Form */}\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div className=\"content-overlay p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Temel Bilgiler</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Çiftlik *\n              </label>\n              <select\n                name=\"farm_id\"\n                value={formData.farm_id}\n                onChange={handleInputChange}\n                required\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n              >\n                {farms.map((farm) => (\n                  <option key={farm.id} value={farm.id}>\n                    {farm.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Irk *\n              </label>\n              <select\n                name=\"breed\"\n                value={formData.breed}\n                onChange={handleInputChange}\n                required\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n              >\n                {breedOptions.map((option) => (\n                  <option key={option.value} value={option.value}>\n                    {option.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Cinsiyet *\n              </label>\n              <select\n                name=\"gender\"\n                value={formData.gender}\n                onChange={handleInputChange}\n                required\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n              >\n                <option value={Gender.FEMALE}>Dişi</option>\n                <option value={Gender.MALE}>Erkek</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Doğum Tarihi *\n              </label>\n              <input\n                type=\"date\"\n                name=\"birth_date\"\n                value={formData.birth_date}\n                onChange={handleInputChange}\n                required\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Mevcut Ağırlık (kg) *\n              </label>\n              <input\n                type=\"number\"\n                name=\"current_weight_kg\"\n                value={formData.current_weight_kg}\n                onChange={handleInputChange}\n                min=\"0\"\n                step=\"0.1\"\n                required\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Vücut Kondisyon Skoru (1-5)\n              </label>\n              <input\n                type=\"number\"\n                name=\"body_condition_score\"\n                value={formData.body_condition_score}\n                onChange={handleInputChange}\n                min=\"1\"\n                max=\"5\"\n                step=\"0.1\"\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Durum\n              </label>\n              <select\n                name=\"status\"\n                value={formData.status}\n                onChange={handleInputChange}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n              >\n                {statusOptions.map((option) => (\n                  <option key={option.value} value={option.value}>\n                    {option.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {formData.gender === Gender.FEMALE && (\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  name=\"is_pregnant\"\n                  checked={formData.is_pregnant}\n                  onChange={handleInputChange}\n                  className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                />\n                <label className=\"ml-2 block text-sm text-gray-700\">\n                  Gebe\n                </label>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Submit Button */}\n        <div className=\"flex justify-end space-x-4\">\n          <Link\n            href={`/animals?farm=${formData.farm_id}`}\n            className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors\"\n          >\n            İptal\n          </Link>\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"inline-flex items-center space-x-2 btn-primary text-white px-6 py-2 rounded-md disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {loading ? (\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n            ) : (\n              <Save className=\"h-4 w-4\" />\n            )}\n            <span>{loading ? 'Güncelleniyor...' : 'Güncelle'}</span>\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,OAAO,EAAE;IAE1B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QACrD,SAAS;QACT,OAAO,wHAAA,CAAA,cAAW,CAAC,KAAK;QACxB,YAAY;QACZ,QAAQ,wHAAA,CAAA,SAAM,CAAC,MAAM;QACrB,mBAAmB;QACnB,sBAAsB;QACtB,QAAQ,wHAAA,CAAA,eAAY,CAAC,IAAI;QACzB,aAAa;IACf;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG;QAAC;KAAS;IAEb,MAAM,WAAW;QACf,IAAI;YACF,eAAe;YACf,MAAM,CAAC,WAAW,WAAW,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAChD,yHAAA,CAAA,UAAO,CAAC,QAAQ;gBAChB,yHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;aACrB;YAED,SAAS;YACT,UAAU;YAEV,yBAAyB;YACzB,YAAY;gBACV,SAAS,WAAW,OAAO;gBAC3B,OAAO,WAAW,KAAK;gBACvB,YAAY,WAAW,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC/C,QAAQ,WAAW,MAAM;gBACzB,mBAAmB,WAAW,iBAAiB;gBAC/C,sBAAsB,WAAW,oBAAoB;gBACrD,QAAQ,WAAW,MAAM;gBACzB,aAAa,WAAW,WAAW;gBACnC,sBAAsB,WAAW,oBAAoB,GAAG,WAAW,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;gBACxG,uBAAuB,WAAW,qBAAqB,GAAG,WAAW,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;gBAC3G,QAAQ,WAAW,MAAM;gBACzB,SAAS,WAAW,OAAO;gBAC3B,gBAAgB,WAAW,cAAc;gBACzC,eAAe,WAAW,aAAa,GAAG,WAAW,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;YACrF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,MAAM,UAAU,AAAC,EAAE,MAAM,CAAsB,OAAO;QAEtD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UACrB,SAAS,WAAW,WAAW,UAAU,IACzC;YACX,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,UAAU,EAAE;YAC7C,MAAM;YACN;QACF;QAEA,IAAI;YACF,WAAW;YACX,MAAM,yHAAA,CAAA,YAAS,CAAC,YAAY,CAAC,UAAU;YACvC,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,SAAS,OAAO,EAAE;QACjD,EAAE,OAAO,KAAK;YACZ,MAAM;YACN,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB;YAAE,OAAO,wHAAA,CAAA,cAAW,CAAC,KAAK;YAAE,OAAO;QAAQ;QAC3C;YAAE,OAAO,wHAAA,CAAA,cAAW,CAAC,QAAQ;YAAE,OAAO;QAAW;QACjD;YAAE,OAAO,wHAAA,CAAA,cAAW,CAAC,SAAS;YAAE,OAAO;QAAY;QACnD;YAAE,OAAO,wHAAA,CAAA,cAAW,CAAC,SAAS;YAAE,OAAO;QAAY;QACnD;YAAE,OAAO,wHAAA,CAAA,cAAW,CAAC,QAAQ;YAAE,OAAO;QAAW;QACjD;YAAE,OAAO,wHAAA,CAAA,cAAW,CAAC,QAAQ;YAAE,OAAO;QAAW;QACjD;YAAE,OAAO,wHAAA,CAAA,cAAW,CAAC,WAAW;YAAE,OAAO;QAAc;QACvD;YAAE,OAAO,wHAAA,CAAA,cAAW,CAAC,gBAAgB;YAAE,OAAO;QAAgB;QAC9D;YAAE,OAAO,wHAAA,CAAA,cAAW,CAAC,SAAS;YAAE,OAAO;QAAQ;KAChD;IAED,MAAM,gBAAgB;QACpB;YAAE,OAAO,wHAAA,CAAA,eAAY,CAAC,IAAI;YAAE,OAAO;QAAS;QAC5C;YAAE,OAAO,wHAAA,CAAA,eAAY,CAAC,KAAK;YAAE,OAAO;QAAO;QAC3C;YAAE,OAAO,wHAAA,CAAA,eAAY,CAAC,QAAQ;YAAE,OAAO;QAAW;QAClD;YAAE,OAAO,wHAAA,CAAA,eAAY,CAAC,SAAS;YAAE,OAAO;QAAO;QAC/C;YAAE,OAAO,wHAAA,CAAA,eAAY,CAAC,cAAc;YAAE,OAAO;QAAe;KAC7D;IAED,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA2C;;;;;;8BAGzD,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAGlC,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAM,CAAC,cAAc,EAAE,SAAS,OAAO,EAAE;wBACzC,WAAU;kCAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;0BAOtC,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,OAAO;gDACvB,UAAU;gDACV,QAAQ;gDACR,WAAU;0DAET,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wDAAqB,OAAO,KAAK,EAAE;kEACjC,KAAK,IAAI;uDADC,KAAK,EAAE;;;;;;;;;;;;;;;;kDAO1B,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,QAAQ;gDACR,WAAU;0DAET,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC;wDAA0B,OAAO,OAAO,KAAK;kEAC3C,OAAO,KAAK;uDADF,OAAO,KAAK;;;;;;;;;;;;;;;;kDAO/B,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,MAAM;gDACtB,UAAU;gDACV,QAAQ;gDACR,WAAU;;kEAEV,6LAAC;wDAAO,OAAO,wHAAA,CAAA,SAAM,CAAC,MAAM;kEAAE;;;;;;kEAC9B,6LAAC;wDAAO,OAAO,wHAAA,CAAA,SAAM,CAAC,IAAI;kEAAE;;;;;;;;;;;;;;;;;;kDAIhC,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,UAAU;gDAC1B,UAAU;gDACV,QAAQ;gDACR,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,iBAAiB;gDACjC,UAAU;gDACV,KAAI;gDACJ,MAAK;gDACL,QAAQ;gDACR,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,oBAAoB;gDACpC,UAAU;gDACV,KAAI;gDACJ,KAAI;gDACJ,MAAK;gDACL,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,MAAM;gDACtB,UAAU;gDACV,WAAU;0DAET,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;wDAA0B,OAAO,OAAO,KAAK;kEAC3C,OAAO,KAAK;uDADF,OAAO,KAAK;;;;;;;;;;;;;;;;oCAO9B,SAAS,MAAM,KAAK,wHAAA,CAAA,SAAM,CAAC,MAAM,kBAChC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,SAAS,SAAS,WAAW;gDAC7B,UAAU;gDACV,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,WAAU;0DAAmC;;;;;;;;;;;;;;;;;;;;;;;;kCAS5D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,cAAc,EAAE,SAAS,OAAO,EAAE;gCACzC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;;oCAET,wBACC,6LAAC;wCAAI,WAAU;;;;;6DAEf,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAElB,6LAAC;kDAAM,UAAU,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlD;GApUwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}