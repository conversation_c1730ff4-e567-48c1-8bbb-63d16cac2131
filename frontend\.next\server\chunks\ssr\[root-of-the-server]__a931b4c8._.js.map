{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Farm, FarmCreate, Animal, AnimalCreate, AnimalStats, Feed } from '@/types';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Farm API\nexport const farmApi = {\n  // Tüm çiftlikleri getir\n  getFarms: async (): Promise<Farm[]> => {\n    const response = await api.get('/farms/');\n    return response.data;\n  },\n\n  // Belirli bir çiftliği getir\n  getFarm: async (farmId: string): Promise<Farm> => {\n    const response = await api.get(`/farms/${farmId}`);\n    return response.data;\n  },\n\n  // Yeni çiftlik oluştur\n  createFarm: async (farmData: FarmCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/farms/', farmData);\n    return response.data;\n  },\n\n  // Çiftlik güncelle\n  updateFarm: async (farmId: string, farmData: FarmCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/farms/${farmId}`, farmData);\n    return response.data;\n  },\n\n  // Çiftlik sil\n  deleteFarm: async (farmId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/farms/${farmId}`);\n    return response.data;\n  },\n};\n\n// Animal API\nexport const animalApi = {\n  // Çiftlikteki hayvanları getir\n  getAnimalsByFarm: async (farmId: string): Promise<Animal[]> => {\n    const response = await api.get(`/animals/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir hayvanı getir\n  getAnimal: async (animalId: string): Promise<Animal> => {\n    const response = await api.get(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Yeni hayvan ekle\n  createAnimal: async (animalData: AnimalCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/animals/', animalData);\n    return response.data;\n  },\n\n  // Hayvan güncelle\n  updateAnimal: async (animalId: string, animalData: AnimalCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/animals/${animalId}`, animalData);\n    return response.data;\n  },\n\n  // Hayvan sil\n  deleteAnimal: async (animalId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftlik hayvan istatistikleri\n  getFarmAnimalStats: async (farmId: string): Promise<AnimalStats> => {\n    const response = await api.get(`/animals/farm/${farmId}/stats`);\n    return response.data;\n  },\n};\n\n// Feed API\nexport const feedApi = {\n  // Çiftlikteki yemleri getir\n  getFeedsByFarm: async (farmId: string): Promise<Feed[]> => {\n    const response = await api.get(`/feeds/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir yemi getir\n  getFeed: async (feedId: string): Promise<Feed> => {\n    const response = await api.get(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yeni yem ekle\n  createFeed: async (feedData: any): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/feeds/', feedData);\n    return response.data;\n  },\n\n  // Yem güncelle\n  updateFeed: async (feedId: string, feedData: any): Promise<{ message: string }> => {\n    const response = await api.put(`/feeds/${feedId}`, feedData);\n    return response.data;\n  },\n\n  // Yem sil\n  deleteFeed: async (feedId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yem türlerini getir\n  getFeedTypesByFarm: async (farmId: string): Promise<Record<string, any[]>> => {\n    const response = await api.get(`/feeds/farm/${farmId}/types`);\n    return response.data;\n  },\n\n  // Örnek yem verileri ekle\n  addSampleFeeds: async (farmId: string): Promise<{ message: string; feeds: string[] }> => {\n    const response = await api.post(`/feeds/farm/${farmId}/sample-feeds`);\n    return response.data;\n  },\n};\n\n// Ration API\nexport const rationApi = {\n  // Besin ihtiyaçlarını hesapla\n  calculateRequirements: async (animalId: string): Promise<any> => {\n    const response = await api.post(`/rations/calculate-requirements?animal_id=${animalId}`);\n    return response.data;\n  },\n\n  // Rasyon optimizasyonu yap\n  optimizeRation: async (request: any): Promise<any> => {\n    const response = await api.post('/rations/optimize', request);\n    return response.data;\n  },\n\n  // Çiftlikteki rasyonları getir\n  getRationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/rations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Rasyon detaylarını getir\n  getRationDetails: async (rationId: string): Promise<any> => {\n    const response = await api.get(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Rasyonu aktif hale getir\n  activateRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.put(`/rations/${rationId}/activate`);\n    return response.data;\n  },\n\n  // Rasyonu sil\n  deleteRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Hayvan için aktif rasyonu getir\n  getActiveRationForAnimal: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/rations/animal/${animalId}/active`);\n    return response.data;\n  },\n};\n\n// Simulation API\nexport const simulationApi = {\n  // Simülasyon çalıştır\n  runSimulation: async (request: any): Promise<any> => {\n    const response = await api.post('/simulations/run', request);\n    return response.data;\n  },\n\n  // Çiftlikteki simülasyonları getir\n  getSimulationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/simulations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Simülasyon detaylarını getir\n  getSimulationDetails: async (simulationId: string): Promise<any> => {\n    const response = await api.get(`/simulations/${simulationId}`);\n    return response.data;\n  },\n\n  // Simülasyonu sil\n  deleteSimulation: async (simulationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/simulations/${simulationId}`);\n    return response.data;\n  },\n};\n\n// Dashboard API\nexport const dashboardApi = {\n  // Dashboard genel bakış\n  getOverview: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/overview/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard finansal veriler\n  getFinancial: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/financial/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard performans verileri\n  getPerformance: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/performance/${farmId}`);\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthApi = {\n  checkHealth: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health', { baseURL: 'http://localhost:8000' });\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGA,MAAM,eAAe;AAErB,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,UAAU;IACrB,wBAAwB;IACxB,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,cAAc,OAAO,UAAkB;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;QACpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,8BAA8B;IAC9B,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,0CAA0C,EAAE,UAAU;QACvF,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,kCAAkC;IAClC,0BAA0B,OAAO;QAC/B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC;QACnE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,gBAAgB;IAC3B,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,mCAAmC;IACnC,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,aAAa,EAAE,cAAc;QAChE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,eAAe;IAC1B,wBAAwB;IACxB,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ;QACjE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,WAAW;YAAE,SAAS;QAAwB;QAC7E,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/app/simulation/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter, useParams } from 'next/navigation';\nimport { \n  ArrowLeft, \n  TrendingUp, \n  AlertCircle,\n  BarChart3,\n  DollarSign,\n  Target,\n  Clock,\n  Users,\n  MapPin,\n  Calendar\n} from 'lucide-react';\nimport { simulationApi } from '@/services/api';\n\ninterface SimulationDetails {\n  simulation: {\n    id: string;\n    name: string;\n    description?: string;\n    start_age_months: number;\n    end_age_months: number;\n    slaughter_age_months?: number;\n    total_feed_cost: number;\n    total_feed_consumption_kg: number;\n    created_at: string;\n    breed?: string;\n    gender?: string;\n    current_weight_kg?: number;\n    farm_name?: string;\n    farm_location?: string;\n  };\n  details: Array<{\n    month: number;\n    age_months: number;\n    weight_kg: number;\n    daily_gain_kg: number;\n    feed_consumption_kg_per_day: number;\n    monthly_feed_cost: number;\n    monthly_feed_consumption_kg: number;\n    physiological_stage: string;\n    is_pregnant: boolean;\n    is_lactating: boolean;\n  }>;\n  summary: {\n    total_months: number;\n    initial_weight_kg: number;\n    final_weight_kg: number;\n    total_weight_gain_kg: number;\n    average_daily_gain_kg: number;\n    average_daily_consumption_kg: number;\n    total_feed_cost: number;\n    total_feed_consumption_kg: number;\n    cost_per_kg_gain: number;\n    feed_conversion_ratio: number;\n    stages_analysis: {\n      [stage: string]: {\n        months: number;\n        total_cost: number;\n        total_consumption: number;\n      };\n    };\n  };\n}\n\nexport default function SimulationDetailPage() {\n  const router = useRouter();\n  const params = useParams();\n  const simulationId = params.id as string;\n  \n  const [details, setDetails] = useState<SimulationDetails | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n\n  useEffect(() => {\n    if (simulationId) {\n      loadSimulationDetails();\n    }\n  }, [simulationId]);\n\n  const loadSimulationDetails = async () => {\n    setLoading(true);\n    try {\n      const data = await simulationApi.getSimulationDetails(simulationId);\n      setDetails(data);\n    } catch (err: any) {\n      setError(err.response?.data?.detail || 'Simülasyon detayları yüklenirken hata oluştu');\n      console.error(err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('tr-TR', {\n      style: 'currency',\n      currency: 'TRY'\n    }).format(amount);\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('tr-TR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getStageLabel = (stage: string) => {\n    const labels: { [key: string]: string } = {\n      'suckling': 'Emziren Buzağı',\n      'weaning': 'Sütten Kesim',\n      'growing': 'Büyüme',\n      'yearling': 'Düve/Tosun',\n      'heifer': 'Düve',\n      'breeding': 'Üreme',\n      'bull': 'Boğa'\n    };\n    return labels[stage] || stage;\n  };\n\n  const getStageColor = (stage: string) => {\n    const colors: { [key: string]: string } = {\n      'suckling': 'bg-pink-100 text-pink-800',\n      'weaning': 'bg-yellow-100 text-yellow-800',\n      'growing': 'bg-green-100 text-green-800',\n      'yearling': 'bg-blue-100 text-blue-800',\n      'heifer': 'bg-purple-100 text-purple-800',\n      'breeding': 'bg-red-100 text-red-800',\n      'bull': 'bg-gray-100 text-gray-800'\n    };\n    return colors[stage] || 'bg-gray-100 text-gray-800';\n  };\n\n  const getSimulationDuration = () => {\n    if (!details) return '';\n    const months = details.simulation.end_age_months - details.simulation.start_age_months;\n    const years = Math.floor(months / 12);\n    const remainingMonths = months % 12;\n    \n    if (years > 0) {\n      return `${years} yıl ${remainingMonths > 0 ? `${remainingMonths} ay` : ''}`;\n    }\n    return `${months} ay`;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Simülasyon detayları yükleniyor...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={() => router.back()}\n            className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n          >\n            <ArrowLeft className=\"h-5 w-5 text-gray-600\" />\n          </button>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Simülasyon Detayları</h1>\n        </div>\n        \n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n          <div className=\"flex\">\n            <AlertCircle className=\"h-5 w-5 text-red-400\" />\n            <div className=\"ml-3\">\n              <p className=\"text-sm text-red-800\">{error}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!details) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={() => router.back()}\n            className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n          >\n            <ArrowLeft className=\"h-5 w-5 text-gray-600\" />\n          </button>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Simülasyon Bulunamadı</h1>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={() => router.back()}\n            className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n          >\n            <ArrowLeft className=\"h-5 w-5 text-gray-600\" />\n          </button>\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">{details.simulation.name}</h1>\n            <p className=\"text-gray-600 mt-1\">\n              {details.simulation.description || 'Yaşam boyu simülasyon analizi'}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Info Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        {/* Çiftlik Bilgisi */}\n        <div className=\"content-overlay p-4\">\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <MapPin className=\"h-4 w-4 text-gray-500\" />\n            <span className=\"text-sm font-medium text-gray-700\">Çiftlik</span>\n          </div>\n          <p className=\"font-semibold text-gray-900\">{details.simulation.farm_name}</p>\n          <p className=\"text-sm text-gray-600\">{details.simulation.farm_location}</p>\n        </div>\n\n        {/* Hayvan Bilgisi */}\n        <div className=\"content-overlay p-4\">\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <Users className=\"h-4 w-4 text-gray-500\" />\n            <span className=\"text-sm font-medium text-gray-700\">Hayvan</span>\n          </div>\n          <p className=\"font-semibold text-gray-900\">\n            {details.simulation.breed} - {details.simulation.gender === 'male' ? 'Erkek' : 'Dişi'}\n          </p>\n          <p className=\"text-sm text-gray-600\">\n            Başlangıç: {details.summary.initial_weight_kg} kg\n          </p>\n        </div>\n\n        {/* Simülasyon Süresi */}\n        <div className=\"content-overlay p-4\">\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <Clock className=\"h-4 w-4 text-gray-500\" />\n            <span className=\"text-sm font-medium text-gray-700\">Süre</span>\n          </div>\n          <p className=\"font-semibold text-gray-900\">{getSimulationDuration()}</p>\n          <p className=\"text-sm text-gray-600\">\n            {details.simulation.start_age_months} - {details.simulation.end_age_months} ay\n          </p>\n        </div>\n\n        {/* Oluşturma Tarihi */}\n        <div className=\"content-overlay p-4\">\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <Calendar className=\"h-4 w-4 text-gray-500\" />\n            <span className=\"text-sm font-medium text-gray-700\">Oluşturulma</span>\n          </div>\n          <p className=\"font-semibold text-gray-900\">\n            {formatDate(details.simulation.created_at)}\n          </p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Sol Kolon - Özet İstatistikler */}\n        <div className=\"space-y-6\">\n          {/* Toplam Sonuçlar */}\n          <div className=\"content-overlay p-6\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <BarChart3 className=\"h-5 w-5 text-green-600\" />\n              <h2 className=\"text-lg font-semibold text-gray-900\">Toplam Sonuçlar</h2>\n            </div>\n            \n            <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"bg-blue-50 p-3 rounded-lg\">\n                  <p className=\"text-sm text-blue-600\">Toplam Maliyet</p>\n                  <p className=\"font-bold text-blue-900 text-lg\">\n                    {formatCurrency(details.summary.total_feed_cost)}\n                  </p>\n                </div>\n                <div className=\"bg-green-50 p-3 rounded-lg\">\n                  <p className=\"text-sm text-green-600\">Toplam Yem</p>\n                  <p className=\"font-bold text-green-900 text-lg\">\n                    {details.summary.total_feed_consumption_kg.toLocaleString('tr-TR')} kg\n                  </p>\n                </div>\n                <div className=\"bg-purple-50 p-3 rounded-lg\">\n                  <p className=\"text-sm text-purple-600\">Ağırlık Artışı</p>\n                  <p className=\"font-bold text-purple-900 text-lg\">\n                    {details.summary.total_weight_gain_kg.toFixed(0)} kg\n                  </p>\n                </div>\n                <div className=\"bg-orange-50 p-3 rounded-lg\">\n                  <p className=\"text-sm text-orange-600\">Final Ağırlık</p>\n                  <p className=\"font-bold text-orange-900 text-lg\">\n                    {details.summary.final_weight_kg.toFixed(0)} kg\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Performans Metrikleri */}\n          <div className=\"content-overlay p-6\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <Target className=\"h-5 w-5 text-blue-600\" />\n              <h2 className=\"text-lg font-semibold text-gray-900\">Performans Metrikleri</h2>\n            </div>\n            \n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Ortalama Günlük Artış:</span>\n                <span className=\"font-semibold text-gray-900\">\n                  {details.summary.average_daily_gain_kg.toFixed(3)} kg/gün\n                </span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Ortalama Günlük Tüketim:</span>\n                <span className=\"font-semibold text-gray-900\">\n                  {details.summary.average_daily_consumption_kg.toFixed(1)} kg/gün\n                </span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Yem Dönüşüm Oranı:</span>\n                <span className=\"font-semibold text-gray-900\">\n                  {details.summary.feed_conversion_ratio.toFixed(1)}\n                </span>\n              </div>\n              <div className=\"flex justify-between items-center border-t pt-3\">\n                <span className=\"text-gray-600 font-medium\">Kg Artış Maliyeti:</span>\n                <span className=\"font-bold text-gray-900\">\n                  {formatCurrency(details.summary.cost_per_kg_gain)}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Sağ Kolon - Dönemsel Analiz */}\n        <div className=\"space-y-6\">\n          {/* Fizyolojik Dönemler */}\n          <div className=\"content-overlay p-6\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <DollarSign className=\"h-5 w-5 text-purple-600\" />\n              <h2 className=\"text-lg font-semibold text-gray-900\">Dönemsel Analiz</h2>\n            </div>\n            \n            <div className=\"space-y-3\">\n              {Object.entries(details.summary.stages_analysis).map(([stage, data]) => (\n                <div key={stage} className=\"border border-gray-200 rounded-lg p-3\">\n                  <div className=\"flex justify-between items-center mb-2\">\n                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStageColor(stage)}`}>\n                      {getStageLabel(stage)}\n                    </span>\n                    <span className=\"text-sm text-gray-600\">{data.months} ay</span>\n                  </div>\n                  \n                  <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                    <div>\n                      <span className=\"text-gray-600\">Maliyet:</span>\n                      <span className=\"font-medium ml-1\">{formatCurrency(data.total_cost)}</span>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600\">Yem:</span>\n                      <span className=\"font-medium ml-1\">{data.total_consumption.toFixed(0)} kg</span>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Aylık Trend (Son 12 ay) */}\n          <div className=\"content-overlay p-6\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <TrendingUp className=\"h-5 w-5 text-orange-600\" />\n              <h2 className=\"text-lg font-semibold text-gray-900\">Son 12 Ay Trendi</h2>\n            </div>\n            \n            <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n              {details.details.slice(-12).map((detail, index) => (\n                <div key={index} className=\"flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0\">\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-900\">\n                      {detail.age_months} ay\n                    </span>\n                    <span className={`ml-2 inline-block px-2 py-1 rounded-full text-xs ${getStageColor(detail.physiological_stage)}`}>\n                      {getStageLabel(detail.physiological_stage)}\n                    </span>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      {detail.weight_kg.toFixed(0)} kg\n                    </div>\n                    <div className=\"text-xs text-gray-500\">\n                      {formatCurrency(detail.monthly_feed_cost)}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAhBA;;;;;;AAoEe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,OAAO,EAAE;IAE9B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc;YAChB;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,wBAAwB;QAC5B,WAAW;QACX,IAAI;YACF,MAAM,OAAO,MAAM,sHAAA,CAAA,gBAAa,CAAC,oBAAoB,CAAC;YACtD,WAAW;QACb,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,UAAU;YACvC,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAoC;YACxC,YAAY;YACZ,WAAW;YACX,WAAW;YACX,YAAY;YACZ,UAAU;YACV,YAAY;YACZ,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,MAAM,IAAI;IAC1B;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAoC;YACxC,YAAY;YACZ,WAAW;YACX,WAAW;YACX,YAAY;YACZ,UAAU;YACV,YAAY;YACZ,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,MAAM,IAAI;IAC1B;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,SAAS,OAAO;QACrB,MAAM,SAAS,QAAQ,UAAU,CAAC,cAAc,GAAG,QAAQ,UAAU,CAAC,gBAAgB;QACtF,MAAM,QAAQ,KAAK,KAAK,CAAC,SAAS;QAClC,MAAM,kBAAkB,SAAS;QAEjC,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,KAAK,EAAE,kBAAkB,IAAI,GAAG,gBAAgB,GAAG,CAAC,GAAG,IAAI;QAC7E;QACA,OAAO,GAAG,OAAO,GAAG,CAAC;IACvB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,OAAO,IAAI;4BAC1B,WAAU;sCAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;;;;;;;8BAGnD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMjD;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI;wBAC1B,WAAU;kCAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIzD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,OAAO,IAAI;4BAC1B,WAAU;sCAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC,QAAQ,UAAU,CAAC,IAAI;;;;;;8CACzE,8OAAC;oCAAE,WAAU;8CACV,QAAQ,UAAU,CAAC,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAE,WAAU;0CAA+B,QAAQ,UAAU,CAAC,SAAS;;;;;;0CACxE,8OAAC;gCAAE,WAAU;0CAAyB,QAAQ,UAAU,CAAC,aAAa;;;;;;;;;;;;kCAIxE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAE,WAAU;;oCACV,QAAQ,UAAU,CAAC,KAAK;oCAAC;oCAAI,QAAQ,UAAU,CAAC,MAAM,KAAK,SAAS,UAAU;;;;;;;0CAEjF,8OAAC;gCAAE,WAAU;;oCAAwB;oCACvB,QAAQ,OAAO,CAAC,iBAAiB;oCAAC;;;;;;;;;;;;;kCAKlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAE,WAAU;0CAA+B;;;;;;0CAC5C,8OAAC;gCAAE,WAAU;;oCACV,QAAQ,UAAU,CAAC,gBAAgB;oCAAC;oCAAI,QAAQ,UAAU,CAAC,cAAc;oCAAC;;;;;;;;;;;;;kCAK/E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAE,WAAU;0CACV,WAAW,QAAQ,UAAU,CAAC,UAAU;;;;;;;;;;;;;;;;;;0BAK/C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;;kDAGtD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEACV,eAAe,QAAQ,OAAO,CAAC,eAAe;;;;;;;;;;;;8DAGnD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAyB;;;;;;sEACtC,8OAAC;4DAAE,WAAU;;gEACV,QAAQ,OAAO,CAAC,yBAAyB,CAAC,cAAc,CAAC;gEAAS;;;;;;;;;;;;;8DAGvE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAA0B;;;;;;sEACvC,8OAAC;4DAAE,WAAU;;gEACV,QAAQ,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAGrD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAA0B;;;;;;sEACvC,8OAAC;4DAAE,WAAU;;gEACV,QAAQ,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;;kDAGtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;;4DACb,QAAQ,OAAO,CAAC,qBAAqB,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAGtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;;4DACb,QAAQ,OAAO,CAAC,4BAA4B,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAG7D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEACb,QAAQ,OAAO,CAAC,qBAAqB,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAGnD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAA4B;;;;;;kEAC5C,8OAAC;wDAAK,WAAU;kEACb,eAAe,QAAQ,OAAO,CAAC,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1D,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;;kDAGtD,8OAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,KAAK,iBACjE,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,wDAAwD,EAAE,cAAc,QAAQ;0EAC/F,cAAc;;;;;;0EAEjB,8OAAC;gEAAK,WAAU;;oEAAyB,KAAK,MAAM;oEAAC;;;;;;;;;;;;;kEAGvD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAoB,eAAe,KAAK,UAAU;;;;;;;;;;;;0EAEpE,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;;4EAAoB,KAAK,iBAAiB,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;;;;;;;+CAflE;;;;;;;;;;;;;;;;0CAwBhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;;kDAGtD,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,QAAQ,sBACvC,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;;oEACb,OAAO,UAAU;oEAAC;;;;;;;0EAErB,8OAAC;gEAAK,WAAW,CAAC,iDAAiD,EAAE,cAAc,OAAO,mBAAmB,GAAG;0EAC7G,cAAc,OAAO,mBAAmB;;;;;;;;;;;;kEAG7C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;oEACZ,OAAO,SAAS,CAAC,OAAO,CAAC;oEAAG;;;;;;;0EAE/B,8OAAC;gEAAI,WAAU;0EACZ,eAAe,OAAO,iBAAiB;;;;;;;;;;;;;+CAdpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyB1B", "debugId": null}}]}