{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Farm, FarmCreate, Animal, AnimalCreate, AnimalStats, Feed } from '@/types';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Farm API\nexport const farmApi = {\n  // Tüm çiftlikleri getir\n  getFarms: async (): Promise<Farm[]> => {\n    const response = await api.get('/farms/');\n    return response.data;\n  },\n\n  // Belirli bir çiftliği getir\n  getFarm: async (farmId: string): Promise<Farm> => {\n    const response = await api.get(`/farms/${farmId}`);\n    return response.data;\n  },\n\n  // Yeni çiftlik oluştur\n  createFarm: async (farmData: FarmCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/farms/', farmData);\n    return response.data;\n  },\n\n  // Çiftlik güncelle\n  updateFarm: async (farmId: string, farmData: FarmCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/farms/${farmId}`, farmData);\n    return response.data;\n  },\n\n  // Çiftlik sil\n  deleteFarm: async (farmId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/farms/${farmId}`);\n    return response.data;\n  },\n};\n\n// Animal API\nexport const animalApi = {\n  // Çiftlikteki hayvanları getir\n  getAnimalsByFarm: async (farmId: string): Promise<Animal[]> => {\n    const response = await api.get(`/animals/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir hayvanı getir\n  getAnimal: async (animalId: string): Promise<Animal> => {\n    const response = await api.get(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Yeni hayvan ekle\n  createAnimal: async (animalData: AnimalCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/animals/', animalData);\n    return response.data;\n  },\n\n  // Hayvan güncelle\n  updateAnimal: async (animalId: string, animalData: AnimalCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/animals/${animalId}`, animalData);\n    return response.data;\n  },\n\n  // Hayvan sil\n  deleteAnimal: async (animalId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftlik hayvan istatistikleri\n  getFarmAnimalStats: async (farmId: string): Promise<AnimalStats> => {\n    const response = await api.get(`/animals/farm/${farmId}/stats`);\n    return response.data;\n  },\n};\n\n// Feed API\nexport const feedApi = {\n  // Çiftlikteki yemleri getir\n  getFeedsByFarm: async (farmId: string): Promise<Feed[]> => {\n    const response = await api.get(`/feeds/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir yemi getir\n  getFeed: async (feedId: string): Promise<Feed> => {\n    const response = await api.get(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yeni yem ekle\n  createFeed: async (feedData: any): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/feeds/', feedData);\n    return response.data;\n  },\n\n  // Yem güncelle\n  updateFeed: async (feedId: string, feedData: any): Promise<{ message: string }> => {\n    const response = await api.put(`/feeds/${feedId}`, feedData);\n    return response.data;\n  },\n\n  // Yem sil\n  deleteFeed: async (feedId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yem türlerini getir\n  getFeedTypesByFarm: async (farmId: string): Promise<Record<string, any[]>> => {\n    const response = await api.get(`/feeds/farm/${farmId}/types`);\n    return response.data;\n  },\n\n  // Örnek yem verileri ekle\n  addSampleFeeds: async (farmId: string): Promise<{ message: string; feeds: string[] }> => {\n    const response = await api.post(`/feeds/farm/${farmId}/sample-feeds`);\n    return response.data;\n  },\n};\n\n// Ration API\nexport const rationApi = {\n  // Besin ihtiyaçlarını hesapla\n  calculateRequirements: async (animalId: string): Promise<any> => {\n    const response = await api.post(`/rations/calculate-requirements?animal_id=${animalId}`);\n    return response.data;\n  },\n\n  // Rasyon optimizasyonu yap\n  optimizeRation: async (request: any): Promise<any> => {\n    const response = await api.post('/rations/optimize', request);\n    return response.data;\n  },\n\n  // Çiftlikteki rasyonları getir\n  getRationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/rations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Rasyon detaylarını getir\n  getRationDetails: async (rationId: string): Promise<any> => {\n    const response = await api.get(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Rasyonu aktif hale getir\n  activateRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.put(`/rations/${rationId}/activate`);\n    return response.data;\n  },\n\n  // Rasyonu sil\n  deleteRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Hayvan için aktif rasyonu getir\n  getActiveRationForAnimal: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/rations/animal/${animalId}/active`);\n    return response.data;\n  },\n};\n\n// Simulation API\nexport const simulationApi = {\n  // Simülasyon çalıştır\n  runSimulation: async (request: any): Promise<any> => {\n    const response = await api.post('/simulations/run', request);\n    return response.data;\n  },\n\n  // Çiftlikteki simülasyonları getir\n  getSimulationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/simulations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Simülasyon detaylarını getir\n  getSimulationDetails: async (simulationId: string): Promise<any> => {\n    const response = await api.get(`/simulations/${simulationId}`);\n    return response.data;\n  },\n\n  // Simülasyonu sil\n  deleteSimulation: async (simulationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/simulations/${simulationId}`);\n    return response.data;\n  },\n};\n\n// Dashboard API\nexport const dashboardApi = {\n  // Dashboard genel bakış\n  getOverview: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/overview/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard finansal veriler\n  getFinancial: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/financial/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard performans verileri\n  getPerformance: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/performance/${farmId}`);\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthApi = {\n  checkHealth: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health', { baseURL: 'http://localhost:8000' });\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGA,MAAM,eAAe;AAErB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,UAAU;IACrB,wBAAwB;IACxB,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,cAAc,OAAO,UAAkB;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;QACpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,8BAA8B;IAC9B,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,0CAA0C,EAAE,UAAU;QACvF,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,kCAAkC;IAClC,0BAA0B,OAAO;QAC/B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC;QACnE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,gBAAgB;IAC3B,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,mCAAmC;IACnC,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,aAAa,EAAE,cAAc;QAChE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,eAAe;IAC1B,wBAAwB;IACxB,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ;QACjE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,WAAW;YAAE,SAAS;QAAwB;QAC7E,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/app/reports/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport {\n  FileText,\n  Download,\n  Calendar,\n  Filter,\n  BarChart3,\n  TrendingUp,\n  DollarSign,\n  Users,\n  Target,\n  AlertCircle,\n  CheckCircle,\n  Clock\n} from 'lucide-react';\nimport { dashboardApi, farmApi, simulationApi } from '@/services/api';\nimport { Farm } from '@/types';\n\nexport default function ReportsPage() {\n  const [farms, setFarms] = useState<Farm[]>([]);\n  const [selectedFarmId, setSelectedFarmId] = useState<string>('');\n  const [reportType, setReportType] = useState<string>('overview');\n  const [dateRange, setDateRange] = useState<string>('30');\n  const [reportData, setReportData] = useState<any>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string>('');\n\n  useEffect(() => {\n    loadFarms();\n  }, []);\n\n  useEffect(() => {\n    if (selectedFarmId && reportType) {\n      generateReport();\n    }\n  }, [selectedFarmId, reportType, dateRange]);\n\n  const loadFarms = async () => {\n    try {\n      const farmsData = await farmApi.getFarms();\n      setFarms(farmsData);\n      if (farmsData.length > 0) {\n        setSelectedFarmId(farmsData[0].id);\n      }\n    } catch (err) {\n      setError('Çiftlikler yüklenirken hata oluştu');\n      console.error(err);\n    }\n  };\n\n  const generateReport = async () => {\n    if (!selectedFarmId) return;\n\n    setLoading(true);\n    setError('');\n\n    try {\n      let data;\n\n      switch (reportType) {\n        case 'overview':\n          data = await dashboardApi.getOverview(selectedFarmId);\n          break;\n        case 'financial':\n          data = await dashboardApi.getFinancial(selectedFarmId);\n          break;\n        case 'performance':\n          data = await dashboardApi.getPerformance(selectedFarmId);\n          break;\n        case 'simulations':\n          data = await simulationApi.getSimulationsByFarm(selectedFarmId);\n          break;\n        default:\n          data = await dashboardApi.getOverview(selectedFarmId);\n      }\n\n      setReportData(data);\n    } catch (err) {\n      setError('Rapor oluşturulurken hata oluştu');\n      console.error(err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const exportReport = () => {\n    if (!reportData) return;\n\n    const reportContent = JSON.stringify(reportData, null, 2);\n    const blob = new Blob([reportContent], { type: 'application/json' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${reportType}-raporu-${new Date().toISOString().split('T')[0]}.json`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('tr-TR', {\n      style: 'currency',\n      currency: 'TRY'\n    }).format(amount);\n  };\n\n  const formatNumber = (num: number, decimals: number = 0) => {\n    return new Intl.NumberFormat('tr-TR', {\n      minimumFractionDigits: decimals,\n      maximumFractionDigits: decimals\n    }).format(num);\n  };\n\n  const getReportTitle = () => {\n    const titles: { [key: string]: string } = {\n      'overview': 'Genel Bakış Raporu',\n      'financial': 'Finansal Analiz Raporu',\n      'performance': 'Performans Raporu',\n      'simulations': 'Simülasyon Raporu'\n    };\n    return titles[reportType] || 'Rapor';\n  };\n\n  const renderOverviewReport = () => {\n    if (!reportData || !reportData.animal_stats) return null;\n\n    return (\n      <div className=\"space-y-6\">\n        {/* Özet Kartlar */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <div className=\"content-overlay p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Users className=\"h-5 w-5 text-blue-600\" />\n              <span className=\"text-sm font-medium text-gray-700\">Toplam Hayvan</span>\n            </div>\n            <p className=\"text-2xl font-bold text-gray-900\">\n              {reportData.animal_stats?.total_animals || 0}\n            </p>\n            <p className=\"text-sm text-gray-600\">\n              {reportData.animal_stats?.male_count || 0} Erkek, {reportData.animal_stats?.female_count || 0} Dişi\n            </p>\n          </div>\n\n          <div className=\"content-overlay p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Target className=\"h-5 w-5 text-green-600\" />\n              <span className=\"text-sm font-medium text-gray-700\">Aktif Rasyonlar</span>\n            </div>\n            <p className=\"text-2xl font-bold text-gray-900\">\n              {reportData.ration_stats?.active_rations || 0}\n            </p>\n            <p className=\"text-sm text-gray-600\">\n              {reportData.ration_stats?.total_rations || 0} toplam rasyon\n            </p>\n          </div>\n\n          <div className=\"content-overlay p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <BarChart3 className=\"h-5 w-5 text-purple-600\" />\n              <span className=\"text-sm font-medium text-gray-700\">Simülasyonlar</span>\n            </div>\n            <p className=\"text-2xl font-bold text-gray-900\">\n              {reportData.simulation_stats?.total_simulations || 0}\n            </p>\n            <p className=\"text-sm text-gray-600\">\n              Ortalama {formatCurrency(reportData.simulation_stats?.avg_simulation_cost || 0)}\n            </p>\n          </div>\n\n          <div className=\"content-overlay p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Clock className=\"h-5 w-5 text-orange-600\" />\n              <span className=\"text-sm font-medium text-gray-700\">Son 30 Gün</span>\n            </div>\n            <p className=\"text-2xl font-bold text-gray-900\">\n              {(reportData.recent_activity?.new_animals_30d || 0) +\n               (reportData.recent_activity?.new_rations_30d || 0) +\n               (reportData.recent_activity?.new_simulations_30d || 0)}\n            </p>\n            <p className=\"text-sm text-gray-600\">Yeni kayıt</p>\n          </div>\n        </div>\n\n        {/* Detaylı İstatistikler */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <div className=\"content-overlay p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Hayvan İstatistikleri</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Ortalama Ağırlık:</span>\n                <span className=\"font-medium\">{formatNumber(reportData.animal_stats?.avg_weight || 0, 1)} kg</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Ortalama Yaş:</span>\n                <span className=\"font-medium\">{formatNumber(reportData.animal_stats?.avg_age_months || 0, 0)} ay</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Erkek Oranı:</span>\n                <span className=\"font-medium\">\n                  %{formatNumber(((reportData.animal_stats?.male_count || 0) / (reportData.animal_stats?.total_animals || 1)) * 100, 1)}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"content-overlay p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Yem İstatistikleri</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Toplam Yem Türü:</span>\n                <span className=\"font-medium\">{reportData.feed_stats?.total_feeds || 0}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Ortalama Maliyet:</span>\n                <span className=\"font-medium\">{formatCurrency(reportData.feed_stats?.avg_cost_per_kg || 0)}/kg</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Konsantre Yem:</span>\n                <span className=\"font-medium\">{reportData.feed_stats?.concentrate_count || 0} adet</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  const renderFinancialReport = () => {\n    if (!reportData || !reportData.daily_costs) return null;\n\n    return (\n      <div className=\"space-y-6\">\n        {/* Maliyet Kartları */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <div className=\"content-overlay p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <DollarSign className=\"h-5 w-5 text-green-600\" />\n              <span className=\"text-sm font-medium text-gray-700\">Günlük Maliyet</span>\n            </div>\n            <p className=\"text-2xl font-bold text-gray-900\">\n              {formatCurrency(reportData.daily_costs?.total_daily_cost || 0)}\n            </p>\n          </div>\n\n          <div className=\"content-overlay p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <TrendingUp className=\"h-5 w-5 text-blue-600\" />\n              <span className=\"text-sm font-medium text-gray-700\">Aylık Maliyet</span>\n            </div>\n            <p className=\"text-2xl font-bold text-gray-900\">\n              {formatCurrency(reportData.daily_costs?.monthly_cost || 0)}\n            </p>\n          </div>\n\n          <div className=\"content-overlay p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <BarChart3 className=\"h-5 w-5 text-purple-600\" />\n              <span className=\"text-sm font-medium text-gray-700\">Yıllık Maliyet</span>\n            </div>\n            <p className=\"text-2xl font-bold text-gray-900\">\n              {formatCurrency(reportData.daily_costs?.yearly_cost || 0)}\n            </p>\n          </div>\n\n          <div className=\"content-overlay p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Target className=\"h-5 w-5 text-orange-600\" />\n              <span className=\"text-sm font-medium text-gray-700\">Günlük KM</span>\n            </div>\n            <p className=\"text-2xl font-bold text-gray-900\">\n              {formatNumber(reportData.daily_costs?.total_daily_dm_kg || 0, 1)} kg\n            </p>\n          </div>\n        </div>\n\n        {/* Aktif Rasyonlar */}\n        {reportData.active_rations && reportData.active_rations.length > 0 && (\n          <div className=\"content-overlay p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Aktif Rasyonlar</h3>\n            <div className=\"space-y-3\">\n              {reportData.active_rations.map((ration: any, index: number) => (\n                <div key={index} className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                  <div>\n                    <p className=\"font-medium text-gray-900\">{ration.name}</p>\n                    <p className=\"text-sm text-gray-600\">{formatNumber(ration.daily_dm_kg, 1)} kg KM/gün</p>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"font-semibold text-gray-900\">{formatCurrency(ration.daily_cost)}</p>\n                    <p className=\"text-sm text-gray-600\">günlük</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  const renderPerformanceReport = () => {\n    if (!reportData || !reportData.breed_performance) return null;\n\n    return (\n      <div className=\"space-y-6\">\n        {/* Irk Performansı */}\n        {reportData.breed_performance && reportData.breed_performance.length > 0 && (\n          <div className=\"content-overlay p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Irk Performansı</h3>\n            <div className=\"space-y-3\">\n              {reportData.breed_performance.map((breed: any, index: number) => (\n                <div key={index} className=\"border border-gray-200 rounded-lg p-4\">\n                  <div className=\"flex justify-between items-center mb-2\">\n                    <h4 className=\"font-medium text-gray-900\">{breed.breed}</h4>\n                    <span className=\"text-sm text-gray-600\">{breed.animal_count} hayvan</span>\n                  </div>\n                  <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                    <div>\n                      <span className=\"text-gray-600\">Ortalama Ağırlık:</span>\n                      <span className=\"font-medium ml-2\">{formatNumber(breed.avg_weight, 0)} kg</span>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600\">Ortalama VKS:</span>\n                      <span className=\"font-medium ml-2\">{formatNumber(breed.avg_bcs, 1)}</span>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* En İyi Performanslar */}\n        {reportData.best_performers && reportData.best_performers.length > 0 && (\n          <div className=\"content-overlay p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">En İyi Performanslar</h3>\n            <div className=\"space-y-3\">\n              {reportData.best_performers.map((performer: any, index: number) => (\n                <div key={index} className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                  <div className=\"flex justify-between items-center mb-2\">\n                    <h4 className=\"font-medium text-green-900\">{performer.name}</h4>\n                    <span className=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full\">\n                      #{index + 1}\n                    </span>\n                  </div>\n                  <div className=\"grid grid-cols-3 gap-4 text-sm\">\n                    <div>\n                      <span className=\"text-green-600\">FCR:</span>\n                      <span className=\"font-medium ml-2\">{formatNumber(performer.feed_conversion_ratio, 1)}</span>\n                    </div>\n                    <div>\n                      <span className=\"text-green-600\">Maliyet:</span>\n                      <span className=\"font-medium ml-2\">{formatCurrency(performer.cost_per_kg_gain)}/kg</span>\n                    </div>\n                    <div>\n                      <span className=\"text-green-600\">Günlük Artış:</span>\n                      <span className=\"font-medium ml-2\">{formatNumber(performer.average_daily_gain_kg, 3)} kg</span>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  const renderSimulationsReport = () => {\n    if (!reportData || !Array.isArray(reportData)) return null;\n\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"content-overlay p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n            Simülasyon Listesi ({reportData.length} adet)\n          </h3>\n          <div className=\"space-y-3\">\n            {reportData.map((simulation: any, index: number) => (\n              <div key={index} className=\"border border-gray-200 rounded-lg p-4\">\n                <div className=\"flex justify-between items-center mb-2\">\n                  <h4 className=\"font-medium text-gray-900\">{simulation.name}</h4>\n                  <span className=\"text-sm text-gray-600\">\n                    {simulation.breed} - {simulation.gender === 'male' ? 'Erkek' : 'Dişi'}\n                  </span>\n                </div>\n                <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-gray-600\">Toplam Maliyet:</span>\n                    <span className=\"font-medium ml-2\">{formatCurrency(simulation.total_feed_cost)}</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-600\">Toplam Yem:</span>\n                    <span className=\"font-medium ml-2\">{formatNumber(simulation.total_feed_consumption_kg, 0)} kg</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-600\">Yaş Aralığı:</span>\n                    <span className=\"font-medium ml-2\">{simulation.start_age_months}-{simulation.end_age_months} ay</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-600\">Oluşturulma:</span>\n                    <span className=\"font-medium ml-2\">\n                      {new Date(simulation.created_at).toLocaleDateString('tr-TR')}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  const renderReportContent = () => {\n    switch (reportType) {\n      case 'overview':\n        return renderOverviewReport();\n      case 'financial':\n        return renderFinancialReport();\n      case 'performance':\n        return renderPerformanceReport();\n      case 'simulations':\n        return renderSimulationsReport();\n      default:\n        return renderOverviewReport();\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Raporlar</h1>\n          <p className=\"text-gray-600 mt-2\">\n            Detaylı analiz ve raporlama\n          </p>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"content-overlay p-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Çiftlik\n            </label>\n            <select\n              value={selectedFarmId}\n              onChange={(e) => setSelectedFarmId(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n            >\n              {farms.map((farm) => (\n                <option key={farm.id} value={farm.id}>\n                  {farm.name}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Rapor Türü\n            </label>\n            <select\n              value={reportType}\n              onChange={(e) => setReportType(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n            >\n              <option value=\"overview\">Genel Bakış</option>\n              <option value=\"financial\">Finansal Analiz</option>\n              <option value=\"performance\">Performans</option>\n              <option value=\"simulations\">Simülasyonlar</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Zaman Aralığı\n            </label>\n            <select\n              value={dateRange}\n              onChange={(e) => setDateRange(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n            >\n              <option value=\"7\">Son 7 Gün</option>\n              <option value=\"30\">Son 30 Gün</option>\n              <option value=\"90\">Son 3 Ay</option>\n              <option value=\"365\">Son 1 Yıl</option>\n            </select>\n          </div>\n\n          <div className=\"flex items-end\">\n            <button\n              onClick={exportReport}\n              disabled={!reportData || loading}\n              className=\"w-full btn-primary text-white py-2 rounded-lg flex items-center justify-center space-x-2 disabled:opacity-50\"\n            >\n              <Download className=\"h-4 w-4\" />\n              <span>Dışa Aktar</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n          <div className=\"flex\">\n            <AlertCircle className=\"h-5 w-5 text-red-400\" />\n            <div className=\"ml-3\">\n              <p className=\"text-sm text-red-800\">{error}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Report Content */}\n      <div className=\"content-overlay p-6\">\n        <div className=\"flex justify-between items-center mb-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">{getReportTitle()}</h2>\n          {reportData && (\n            <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n              <CheckCircle className=\"h-4 w-4 text-green-500\" />\n              <span>Rapor oluşturuldu: {new Date().toLocaleString('tr-TR')}</span>\n            </div>\n          )}\n        </div>\n\n        {loading ? (\n          <div className=\"text-center py-8\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Rapor oluşturuluyor...</p>\n          </div>\n        ) : (\n          renderReportContent()\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;AAjBA;;;;AAoBe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,kBAAkB,YAAY;gBAChC;YACF;QACF;gCAAG;QAAC;QAAgB;QAAY;KAAU;IAE1C,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,YAAY,MAAM,yHAAA,CAAA,UAAO,CAAC,QAAQ;YACxC,SAAS;YACT,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,kBAAkB,SAAS,CAAC,EAAE,CAAC,EAAE;YACnC;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,gBAAgB;QAErB,WAAW;QACX,SAAS;QAET,IAAI;YACF,IAAI;YAEJ,OAAQ;gBACN,KAAK;oBACH,OAAO,MAAM,yHAAA,CAAA,eAAY,CAAC,WAAW,CAAC;oBACtC;gBACF,KAAK;oBACH,OAAO,MAAM,yHAAA,CAAA,eAAY,CAAC,YAAY,CAAC;oBACvC;gBACF,KAAK;oBACH,OAAO,MAAM,yHAAA,CAAA,eAAY,CAAC,cAAc,CAAC;oBACzC;gBACF,KAAK;oBACH,OAAO,MAAM,yHAAA,CAAA,gBAAa,CAAC,oBAAoB,CAAC;oBAChD;gBACF;oBACE,OAAO,MAAM,yHAAA,CAAA,eAAY,CAAC,WAAW,CAAC;YAC1C;YAEA,cAAc;QAChB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,YAAY;QAEjB,MAAM,gBAAgB,KAAK,SAAS,CAAC,YAAY,MAAM;QACvD,MAAM,OAAO,IAAI,KAAK;YAAC;SAAc,EAAE;YAAE,MAAM;QAAmB;QAClE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,GAAG,WAAW,QAAQ,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;QAClF,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,eAAe,CAAC,KAAa,WAAmB,CAAC;QACrD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,iBAAiB;QACrB,MAAM,SAAoC;YACxC,YAAY;YACZ,aAAa;YACb,eAAe;YACf,eAAe;QACjB;QACA,OAAO,MAAM,CAAC,WAAW,IAAI;IAC/B;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,cAAc,CAAC,WAAW,YAAY,EAAE,OAAO;QAEpD,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;8CAEtD,6LAAC;oCAAE,WAAU;8CACV,WAAW,YAAY,EAAE,iBAAiB;;;;;;8CAE7C,6LAAC;oCAAE,WAAU;;wCACV,WAAW,YAAY,EAAE,cAAc;wCAAE;wCAAS,WAAW,YAAY,EAAE,gBAAgB;wCAAE;;;;;;;;;;;;;sCAIlG,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;8CAEtD,6LAAC;oCAAE,WAAU;8CACV,WAAW,YAAY,EAAE,kBAAkB;;;;;;8CAE9C,6LAAC;oCAAE,WAAU;;wCACV,WAAW,YAAY,EAAE,iBAAiB;wCAAE;;;;;;;;;;;;;sCAIjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;8CAEtD,6LAAC;oCAAE,WAAU;8CACV,WAAW,gBAAgB,EAAE,qBAAqB;;;;;;8CAErD,6LAAC;oCAAE,WAAU;;wCAAwB;wCACzB,eAAe,WAAW,gBAAgB,EAAE,uBAAuB;;;;;;;;;;;;;sCAIjF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;8CAEtD,6LAAC;oCAAE,WAAU;8CACV,CAAC,WAAW,eAAe,EAAE,mBAAmB,CAAC,IACjD,CAAC,WAAW,eAAe,EAAE,mBAAmB,CAAC,IACjD,CAAC,WAAW,eAAe,EAAE,uBAAuB,CAAC;;;;;;8CAExD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAKzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;;wDAAe,aAAa,WAAW,YAAY,EAAE,cAAc,GAAG;wDAAG;;;;;;;;;;;;;sDAE3F,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;;wDAAe,aAAa,WAAW,YAAY,EAAE,kBAAkB,GAAG;wDAAG;;;;;;;;;;;;;sDAE/F,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;;wDAAc;wDAC1B,aAAa,AAAC,CAAC,WAAW,YAAY,EAAE,cAAc,CAAC,IAAI,CAAC,WAAW,YAAY,EAAE,iBAAiB,CAAC,IAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;sCAM3H,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DAAe,WAAW,UAAU,EAAE,eAAe;;;;;;;;;;;;sDAEvE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;;wDAAe,eAAe,WAAW,UAAU,EAAE,mBAAmB;wDAAG;;;;;;;;;;;;;sDAE7F,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;;wDAAe,WAAW,UAAU,EAAE,qBAAqB;wDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAO3F;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,cAAc,CAAC,WAAW,WAAW,EAAE,OAAO;QAEnD,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;8CAEtD,6LAAC;oCAAE,WAAU;8CACV,eAAe,WAAW,WAAW,EAAE,oBAAoB;;;;;;;;;;;;sCAIhE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;8CAEtD,6LAAC;oCAAE,WAAU;8CACV,eAAe,WAAW,WAAW,EAAE,gBAAgB;;;;;;;;;;;;sCAI5D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;8CAEtD,6LAAC;oCAAE,WAAU;8CACV,eAAe,WAAW,WAAW,EAAE,eAAe;;;;;;;;;;;;sCAI3D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;8CAEtD,6LAAC;oCAAE,WAAU;;wCACV,aAAa,WAAW,WAAW,EAAE,qBAAqB,GAAG;wCAAG;;;;;;;;;;;;;;;;;;;gBAMtE,WAAW,cAAc,IAAI,WAAW,cAAc,CAAC,MAAM,GAAG,mBAC/D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,cAAc,CAAC,GAAG,CAAC,CAAC,QAAa,sBAC3C,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,IAAI;;;;;;8DACrD,6LAAC;oDAAE,WAAU;;wDAAyB,aAAa,OAAO,WAAW,EAAE;wDAAG;;;;;;;;;;;;;sDAE5E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAA+B,eAAe,OAAO,UAAU;;;;;;8DAC5E,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;mCAP/B;;;;;;;;;;;;;;;;;;;;;;IAgBxB;IAEA,MAAM,0BAA0B;QAC9B,IAAI,CAAC,cAAc,CAAC,WAAW,iBAAiB,EAAE,OAAO;QAEzD,qBACE,6LAAC;YAAI,WAAU;;gBAEZ,WAAW,iBAAiB,IAAI,WAAW,iBAAiB,CAAC,MAAM,GAAG,mBACrE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,iBAAiB,CAAC,GAAG,CAAC,CAAC,OAAY,sBAC7C,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA6B,MAAM,KAAK;;;;;;8DACtD,6LAAC;oDAAK,WAAU;;wDAAyB,MAAM,YAAY;wDAAC;;;;;;;;;;;;;sDAE9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAoB,aAAa,MAAM,UAAU,EAAE;gEAAG;;;;;;;;;;;;;8DAExE,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAoB,aAAa,MAAM,OAAO,EAAE;;;;;;;;;;;;;;;;;;;mCAZ5D;;;;;;;;;;;;;;;;gBAsBjB,WAAW,eAAe,IAAI,WAAW,eAAe,CAAC,MAAM,GAAG,mBACjE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,eAAe,CAAC,GAAG,CAAC,CAAC,WAAgB,sBAC/C,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA8B,UAAU,IAAI;;;;;;8DAC1D,6LAAC;oDAAK,WAAU;;wDAA6D;wDACzE,QAAQ;;;;;;;;;;;;;sDAGd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;sEACjC,6LAAC;4DAAK,WAAU;sEAAoB,aAAa,UAAU,qBAAqB,EAAE;;;;;;;;;;;;8DAEpF,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;sEACjC,6LAAC;4DAAK,WAAU;;gEAAoB,eAAe,UAAU,gBAAgB;gEAAE;;;;;;;;;;;;;8DAEjF,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;sEACjC,6LAAC;4DAAK,WAAU;;gEAAoB,aAAa,UAAU,qBAAqB,EAAE;gEAAG;;;;;;;;;;;;;;;;;;;;mCAlBjF;;;;;;;;;;;;;;;;;;;;;;IA4BxB;IAEA,MAAM,0BAA0B;QAC9B,IAAI,CAAC,cAAc,CAAC,MAAM,OAAO,CAAC,aAAa,OAAO;QAEtD,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAA2C;4BAClC,WAAW,MAAM;4BAAC;;;;;;;kCAEzC,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,YAAiB,sBAChC,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA6B,WAAW,IAAI;;;;;;0DAC1D,6LAAC;gDAAK,WAAU;;oDACb,WAAW,KAAK;oDAAC;oDAAI,WAAW,MAAM,KAAK,SAAS,UAAU;;;;;;;;;;;;;kDAGnE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAAoB,eAAe,WAAW,eAAe;;;;;;;;;;;;0DAE/E,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAAoB,aAAa,WAAW,yBAAyB,EAAE;4DAAG;;;;;;;;;;;;;0DAE5F,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAAoB,WAAW,gBAAgB;4DAAC;4DAAE,WAAW,cAAc;4DAAC;;;;;;;;;;;;;0DAE9F,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEACb,IAAI,KAAK,WAAW,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;+BAvBlD;;;;;;;;;;;;;;;;;;;;;IAiCtB;IAEA,MAAM,sBAAsB;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;0BAOtC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oCACjD,WAAU;8CAET,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;4CAAqB,OAAO,KAAK,EAAE;sDACjC,KAAK,IAAI;2CADC,KAAK,EAAE;;;;;;;;;;;;;;;;sCAO1B,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,6LAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,6LAAC;4CAAO,OAAM;sDAAc;;;;;;sDAC5B,6LAAC;4CAAO,OAAM;sDAAc;;;;;;;;;;;;;;;;;;sCAIhC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAI;;;;;;sDAClB,6LAAC;4CAAO,OAAM;sDAAK;;;;;;sDACnB,6LAAC;4CAAO,OAAM;sDAAK;;;;;;sDACnB,6LAAC;4CAAO,OAAM;sDAAM;;;;;;;;;;;;;;;;;;sCAIxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,UAAU,CAAC,cAAc;gCACzB,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOb,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;4BACpD,4BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;;4CAAK;4CAAoB,IAAI,OAAO,cAAc,CAAC;;;;;;;;;;;;;;;;;;;oBAKzD,wBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;+BAG/B;;;;;;;;;;;;;AAKV;GA3gBwB;KAAA", "debugId": null}}]}