'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  Calculator, 
  Plus, 
  Eye, 
  Trash2, 
  CheckCircle,
  AlertCircle,
  TrendingUp,
  DollarSign
} from 'lucide-react';
import { rationApi, farmApi } from '@/services/api';
import { Ration, Farm } from '@/types';

export default function RationsPage() {
  const [rations, setRations] = useState<Ration[]>([]);
  const [farms, setFarms] = useState<Farm[]>([]);
  const [selectedFarmId, setSelectedFarmId] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    loadFarms();
  }, []);

  useEffect(() => {
    if (selectedFarmId) {
      loadRations();
    }
  }, [selectedFarmId]);

  const loadFarms = async () => {
    try {
      const farmsData = await farmApi.getFarms();
      setFarms(farmsData);
      if (farmsData.length > 0) {
        setSelectedFarmId(farmsData[0].id);
      }
    } catch (err) {
      setError('Çiftlikler yüklenirken hata oluştu');
      console.error(err);
    }
  };

  const loadRations = async () => {
    if (!selectedFarmId) return;
    
    setLoading(true);
    try {
      const rationsData = await rationApi.getRationsByFarm(selectedFarmId);
      setRations(rationsData);
    } catch (err) {
      setError('Rasyonlar yüklenirken hata oluştu');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRation = async (rationId: string) => {
    if (!confirm('Bu rasyonu silmek istediğinizden emin misiniz?')) return;

    try {
      await rationApi.deleteRation(rationId);
      await loadRations();
    } catch (err) {
      setError('Rasyon silinirken hata oluştu');
      console.error(err);
    }
  };

  const handleActivateRation = async (rationId: string) => {
    try {
      await rationApi.activateRation(rationId);
      await loadRations();
    } catch (err) {
      setError('Rasyon aktif hale getirilirken hata oluştu');
      console.error(err);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount);
  };

  if (loading && !selectedFarmId) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Yükleniyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Rasyon Yönetimi</h1>
          <p className="text-gray-600 mt-2">
            Hayvanlarınız için optimal rasyon hesaplamaları yapın ve yönetin
          </p>
        </div>
        <Link
          href="/rations/optimize"
          className="btn-primary text-white px-6 py-3 rounded-lg flex items-center space-x-2 hover-glow"
        >
          <Calculator className="h-5 w-5" />
          <span>Rasyon Optimize Et</span>
        </Link>
      </div>

      {/* Farm Selection */}
      {farms.length > 0 && (
        <div className="content-overlay p-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Çiftlik Seçin
          </label>
          <select
            value={selectedFarmId}
            onChange={(e) => setSelectedFarmId(e.target.value)}
            className="w-full max-w-md px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            {farms.map((farm) => (
              <option key={farm.id} value={farm.id}>
                {farm.name} - {farm.location}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Rations List */}
      {selectedFarmId && (
        <div className="space-y-4">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Rasyonlar yükleniyor...</p>
            </div>
          ) : rations.length === 0 ? (
            <div className="content-overlay p-8 text-center">
              <Calculator className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Henüz rasyon bulunmuyor
              </h3>
              <p className="text-gray-600 mb-4">
                Hayvanlarınız için optimal rasyon hesaplaması yapmaya başlayın
              </p>
              <Link
                href="/rations/optimize"
                className="btn-primary text-white px-6 py-3 rounded-lg inline-flex items-center space-x-2"
              >
                <Plus className="h-5 w-5" />
                <span>İlk Rasyonu Oluştur</span>
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {rations.map((ration) => (
                <div
                  key={ration.id}
                  className={`content-overlay hover-glow card-animate p-6 ${
                    ration.is_active ? 'ring-2 ring-green-500' : ''
                  }`}
                >
                  {/* Header */}
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {ration.name}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {ration.ration_type === 'individual' ? 'Bireysel' : 
                         ration.ration_type === 'group' ? 'Grup' : 'Şablon'}
                      </p>
                    </div>
                    {ration.is_active && (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    )}
                  </div>

                  {/* Stats */}
                  <div className="space-y-3 mb-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Günlük Maliyet:</span>
                      <span className="font-medium text-gray-900">
                        {formatCurrency(ration.total_cost_per_day)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Kuru Madde:</span>
                      <span className="font-medium text-gray-900">
                        {ration.total_dry_matter_kg.toFixed(1)} kg
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Ham Protein:</span>
                      <span className="font-medium text-gray-900">
                        {ration.total_crude_protein_percentage.toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">ME:</span>
                      <span className="font-medium text-gray-900">
                        {ration.total_metabolizable_energy_mcal.toFixed(1)} Mcal
                      </span>
                    </div>
                  </div>

                  {/* Optimization Badge */}
                  {ration.is_optimized && (
                    <div className="flex items-center space-x-1 mb-4">
                      <TrendingUp className="h-4 w-4 text-blue-500" />
                      <span className="text-xs text-blue-600 font-medium">
                        Optimize Edilmiş
                      </span>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex space-x-2">
                    <Link
                      href={`/rations/${ration.id}`}
                      className="flex-1 bg-blue-50 text-blue-600 px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-100 transition-colors flex items-center justify-center space-x-1"
                    >
                      <Eye className="h-4 w-4" />
                      <span>Detay</span>
                    </Link>
                    
                    {!ration.is_active && (
                      <button
                        onClick={() => handleActivateRation(ration.id)}
                        className="flex-1 bg-green-50 text-green-600 px-3 py-2 rounded-md text-sm font-medium hover:bg-green-100 transition-colors flex items-center justify-center space-x-1"
                      >
                        <CheckCircle className="h-4 w-4" />
                        <span>Aktif Et</span>
                      </button>
                    )}
                    
                    <button
                      onClick={() => handleDeleteRation(ration.id)}
                      className="bg-red-50 text-red-600 px-3 py-2 rounded-md text-sm font-medium hover:bg-red-100 transition-colors flex items-center justify-center"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
