'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Save } from 'lucide-react';
import { feedApi, farmApi } from '@/services/api';
import { Farm, FeedType, Feed } from '@/types';

interface FeedUpdate {
  farm_id: string;
  name: string;
  feed_type: FeedType;
  cost_per_kg: number;
  dry_matter_percentage: number;
  crude_protein_percentage: number;
  metabolizable_energy_mcal_kg: number;
  storage_life_days: number;
  moisture_content_percentage: number;
}

export default function EditFeedPage() {
  const router = useRouter();
  const params = useParams();
  const feedId = params.id as string;
  
  const [farms, setFarms] = useState<Farm[]>([]);
  const [feed, setFeed] = useState<Feed | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(true);
  const [formData, setFormData] = useState<FeedUpdate>({
    farm_id: '',
    name: '',
    feed_type: FeedType.CONCENTRATE,
    cost_per_kg: 0,
    dry_matter_percentage: 0,
    crude_protein_percentage: 0,
    metabolizable_energy_mcal_kg: 0,
    storage_life_days: 0,
    moisture_content_percentage: 0,
  });

  useEffect(() => {
    loadData();
  }, [feedId]);

  const loadData = async () => {
    try {
      setLoadingData(true);
      const [farmsData, feedData] = await Promise.all([
        farmApi.getFarms(),
        feedApi.getFeed(feedId)
      ]);
      
      setFarms(farmsData);
      setFeed(feedData);
      
      // Form verilerini doldur
      setFormData({
        farm_id: feedData.farm_id,
        name: feedData.name,
        feed_type: feedData.feed_type as FeedType,
        cost_per_kg: feedData.cost_per_kg,
        dry_matter_percentage: feedData.dry_matter_percentage,
        crude_protein_percentage: feedData.crude_protein_percentage,
        metabolizable_energy_mcal_kg: feedData.metabolizable_energy_mcal_kg,
        storage_life_days: feedData.storage_life_days,
        moisture_content_percentage: feedData.moisture_content_percentage,
      });
    } catch (err) {
      console.error('Error loading data:', err);
      alert('Veri yüklenirken hata oluştu');
    } finally {
      setLoadingData(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.farm_id || !formData.name) {
      alert('Çiftlik ve yem adı zorunludur');
      return;
    }

    try {
      setLoading(true);
      await feedApi.updateFeed(feedId, formData);
      router.push(`/feeds?farm=${formData.farm_id}`);
    } catch (err) {
      alert('Yem güncellenirken hata oluştu');
      console.error('Error updating feed:', err);
    } finally {
      setLoading(false);
    }
  };

  const feedTypeOptions = [
    { value: FeedType.CONCENTRATE, label: 'Konsantre' },
    { value: FeedType.ROUGHAGE, label: 'Kaba Yem' },
    { value: FeedType.HAY, label: 'Kuru Ot' },
    { value: FeedType.SILAGE, label: 'Silaj' },
    { value: FeedType.PASTURE, label: 'Mera' },
    { value: FeedType.MINERAL_VITAMIN, label: 'Mineral-Vitamin' },
  ];

  if (loadingData) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Yem bilgileri yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (!feed) {
    return (
      <div className="text-center py-12">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Yem bulunamadı
        </h3>
        <p className="text-gray-600 mb-6">
          Aradığınız yem mevcut değil
        </p>
        <Link
          href="/feeds"
          className="btn-primary text-white px-6 py-3 rounded-md"
        >
          Yem Listesine Dön
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link
          href={`/feeds/${feedId}`}
          className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
        >
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Yem Düzenle</h1>
          <p className="text-gray-600 mt-1">
            Yem bilgilerini güncelleyin
          </p>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="content-overlay p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Temel Bilgiler</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Çiftlik *
              </label>
              <select
                name="farm_id"
                value={formData.farm_id}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                {farms.map((farm) => (
                  <option key={farm.id} value={farm.id}>
                    {farm.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Yem Adı *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Örn: Premium Konsantre"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Yem Türü *
              </label>
              <select
                name="feed_type"
                value={formData.feed_type}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                {feedTypeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Maliyet (₺/kg) *
              </label>
              <input
                type="number"
                name="cost_per_kg"
                value={formData.cost_per_kg}
                onChange={handleInputChange}
                min="0"
                step="0.01"
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        <div className="content-overlay p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Besin Değerleri</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Kuru Madde (%)
              </label>
              <input
                type="number"
                name="dry_matter_percentage"
                value={formData.dry_matter_percentage}
                onChange={handleInputChange}
                min="0"
                max="100"
                step="0.1"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ham Protein (%)
              </label>
              <input
                type="number"
                name="crude_protein_percentage"
                value={formData.crude_protein_percentage}
                onChange={handleInputChange}
                min="0"
                max="100"
                step="0.1"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Metabolik Enerji (Mcal/kg)
              </label>
              <input
                type="number"
                name="metabolizable_energy_mcal_kg"
                value={formData.metabolizable_energy_mcal_kg}
                onChange={handleInputChange}
                min="0"
                step="0.1"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nem İçeriği (%)
              </label>
              <input
                type="number"
                name="moisture_content_percentage"
                value={formData.moisture_content_percentage}
                onChange={handleInputChange}
                min="0"
                max="100"
                step="0.1"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Depolama Ömrü (gün)
              </label>
              <input
                type="number"
                name="storage_life_days"
                value={formData.storage_life_days}
                onChange={handleInputChange}
                min="0"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Link
            href={`/feeds/${feedId}`}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            İptal
          </Link>
          <button
            type="submit"
            disabled={loading}
            className="inline-flex items-center space-x-2 btn-primary text-white px-6 py-2 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            <span>{loading ? 'Güncelleniyor...' : 'Güncelle'}</span>
          </button>
        </div>
      </form>
    </div>
  );
}
