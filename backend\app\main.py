from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from typing import List

try:
    from .database.database import get_db, create_tables
    from .database import models
    from .models import schemas
except ImportError:
    # Eğer relative import çalışmazsa absolute import dene
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))

    from database.database import get_db, create_tables
    from database import models
    from models import schemas

# Veritabanı tablolarını oluştur
create_tables()

# FastAPI uygulaması
app = FastAPI(
    title="Hayvan Yetiştiriciliği Simülasyon Sistemi",
    description="Sığır yetiştiriciliği için kapsamlı simülasyon ve yönetim sistemi",
    version="1.0.0"
)

# CORS middleware ekle
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React frontend
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API router'ları dahil et
try:
    print("Attempting to import routers...")
    try:
        from .api import farms, animals, feeds, rations
        print("✓ Relative imports successful")
    except ImportError as e:
        print(f"Relative import failed: {e}")
        from api import farms, animals, feeds, rations
        print("✓ Absolute imports successful")

    app.include_router(farms.router, prefix="/api/farms", tags=["farms"])
    app.include_router(animals.router, prefix="/api/animals", tags=["animals"])
    app.include_router(feeds.router, prefix="/api/feeds", tags=["feeds"])
    app.include_router(rations.router, prefix="/api", tags=["rations"])
    print("✓ API routers loaded successfully")
except ImportError as e:
    print(f"Router import error: {e}")
    # Basit endpoint'ler ekle
    @app.get("/api/farms/")
    async def get_farms():
        return []

    @app.get("/api/animals/farm/{farm_id}")
    async def get_animals(farm_id: str):
        return []

    @app.get("/api/feeds/farm/{farm_id}")
    async def get_feeds(farm_id: str):
        return []

    @app.get("/api/rations/farm/{farm_id}")
    async def get_rations(farm_id: str):
        return []

    @app.post("/api/rations/calculate-requirements")
    async def calculate_requirements():
        return {"error": "Router not loaded"}

@app.get("/")
async def root():
    return {
        "message": "Hayvan Yetiştiriciliği Simülasyon Sistemi API",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
