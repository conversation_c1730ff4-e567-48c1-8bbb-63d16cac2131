from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()

class Farm(Base):
    __tablename__ = "farms"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False)
    location = Column(String, nullable=False)
    established_date = Column(DateTime, nullable=False)

    # Infrastructure
    total_land_hectares = Column(Float, nullable=False)
    pasture_land_hectares = Column(Float, nullable=False)
    barn_capacity = Column(Integer, nullable=False)
    feed_storage_capacity_tons = Column(Float, nullable=False)
    silage_capacity_tons = Column(Float, nullable=False)
    hay_storage_capacity_tons = Column(Float, nullable=False)
    water_storage_capacity_liters = Column(Float, nullable=False)
    milking_parlor_capacity = Column(Integer, nullable=True)
    quarantine_facility_capacity = Column(Integer, nullable=False)
    hospital_pen_capacity = Column(Integer, nullable=False)
    handling_facility_present = Column(Boolean, nullable=False)
    scale_capacity_kg = Column(Float, nullable=False)

    # Operating Costs (monthly)
    labor_cost_monthly = Column(Float, nullable=False)
    electricity_cost_monthly = Column(Float, nullable=False)
    water_cost_monthly = Column(Float, nullable=False)
    fuel_cost_monthly = Column(Float, nullable=False)
    insurance_cost_monthly = Column(Float, nullable=False)
    maintenance_cost_monthly = Column(Float, nullable=False)

    # Operating Costs (annual)
    veterinary_cost_annual = Column(Float, nullable=False)
    taxation_annual = Column(Float, nullable=False)
    depreciation_annual = Column(Float, nullable=False)
    interest_cost_annual = Column(Float, nullable=False)

    # Operating Costs (per animal)
    vaccination_cost_per_animal = Column(Float, nullable=False)
    deworming_cost_per_animal = Column(Float, nullable=False)
    hoof_care_cost_per_animal = Column(Float, nullable=False)
    breeding_cost_per_service = Column(Float, nullable=False)

    # Market Prices
    live_cattle_price_per_kg = Column(Float, nullable=False)
    calf_price_per_head = Column(Float, nullable=False)
    bull_price_per_head = Column(Float, nullable=False)
    cow_price_per_head = Column(Float, nullable=False)
    carcass_price_per_kg = Column(Float, nullable=False)
    manure_price_per_ton = Column(Float, nullable=False)
    hide_price_per_piece = Column(Float, nullable=False)

    # Production Parameters
    conception_rate = Column(Float, nullable=False)
    calving_rate = Column(Float, nullable=False)
    calf_survival_rate = Column(Float, nullable=False)
    weaning_rate = Column(Float, nullable=False)
    mortality_rate_adult = Column(Float, nullable=False)
    mortality_rate_young = Column(Float, nullable=False)
    culling_rate = Column(Float, nullable=False)
    replacement_rate = Column(Float, nullable=False)

    # Risk Parameters
    disease_outbreak_probability = Column(Float, nullable=False)
    market_volatility_coefficient = Column(Float, nullable=False)
    weather_risk_probability = Column(Float, nullable=False)
    feed_price_volatility = Column(Float, nullable=False)
    drought_probability = Column(Float, nullable=False)
    flood_probability = Column(Float, nullable=False)
    policy_change_risk = Column(Float, nullable=False)

    # Simulation Parameters
    simulation_duration_years = Column(Integer, nullable=False, default=5)
    time_step_days = Column(Integer, nullable=False, default=1)
    monte_carlo_iterations = Column(Integer, nullable=False, default=1000)
    confidence_interval = Column(Float, nullable=False, default=0.95)
    discount_rate = Column(Float, nullable=False)
    inflation_rate = Column(Float, nullable=False)
    tax_rate = Column(Float, nullable=False)

    # Relationships
    animals = relationship("Animal", back_populates="farm")
    feeds = relationship("Feed", back_populates="farm")
    cash_flows = relationship("CashFlow", back_populates="farm")
    rations = relationship("Ration", back_populates="farm")

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Animal(Base):
    __tablename__ = "animals"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    farm_id = Column(String, ForeignKey("farms.id"), nullable=False)

    breed = Column(String, nullable=False)
    birth_date = Column(DateTime, nullable=False)
    gender = Column(String, nullable=False)
    current_weight_kg = Column(Float, nullable=False)
    body_condition_score = Column(Float, nullable=False)
    status = Column(String, nullable=False)

    is_pregnant = Column(Boolean, default=False)
    pregnancy_start_date = Column(DateTime, nullable=True)
    expected_calving_date = Column(DateTime, nullable=True)

    dam_id = Column(String, nullable=True)
    sire_id = Column(String, nullable=True)
    purchase_price = Column(Float, nullable=True)
    purchase_date = Column(DateTime, nullable=True)
    weaning_date = Column(DateTime, nullable=True)
    last_health_check = Column(DateTime, nullable=True)

    vaccination_records = Column(JSON, default=list)
    health_issues = Column(JSON, default=list)

    # Performance records
    total_feed_consumed_kg = Column(Float, default=0.0)
    total_health_costs = Column(Float, default=0.0)
    average_daily_gain_actual = Column(Float, nullable=True)
    feed_conversion_actual = Column(Float, nullable=True)

    # Relationships
    farm = relationship("Farm", back_populates="animals")
    rations = relationship("Ration", back_populates="animal")

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Feed(Base):
    __tablename__ = "feeds"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    farm_id = Column(String, ForeignKey("farms.id"), nullable=False)

    name = Column(String, nullable=False)
    feed_type = Column(String, nullable=False)
    cost_per_kg = Column(Float, nullable=False)
    storage_life_days = Column(Integer, nullable=False)
    moisture_content_percentage = Column(Float, nullable=False)

    # Nutrition values
    dry_matter_percentage = Column(Float, nullable=False)
    crude_protein_percentage = Column(Float, nullable=False)
    metabolizable_energy_mcal_kg = Column(Float, nullable=False)
    net_energy_maintenance_mcal_kg = Column(Float, nullable=False)
    net_energy_gain_mcal_kg = Column(Float, nullable=False)
    crude_fiber_percentage = Column(Float, nullable=False)
    acid_detergent_fiber_percentage = Column(Float, nullable=False)
    neutral_detergent_fiber_percentage = Column(Float, nullable=False)
    total_digestible_nutrients_percentage = Column(Float, nullable=False)
    calcium_percentage = Column(Float, nullable=False)
    phosphorus_percentage = Column(Float, nullable=False)
    rumen_degradable_protein_percentage = Column(Float, nullable=False)
    rumen_undegradable_protein_percentage = Column(Float, nullable=False)

    # Seasonal availability (JSON)
    availability_seasonal = Column(JSON, default=dict)

    # Relationships
    farm = relationship("Farm", back_populates="feeds")

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class BreedCharacteristic(Base):
    __tablename__ = "breed_characteristics"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    breed = Column(String, nullable=False, unique=True)

    mature_weight_male_kg = Column(Float, nullable=False)
    mature_weight_female_kg = Column(Float, nullable=False)
    birth_weight_kg = Column(Float, nullable=False)
    weaning_weight_kg = Column(Float, nullable=False)
    yearling_weight_kg = Column(Float, nullable=False)
    average_daily_gain_kg = Column(Float, nullable=False)
    feed_conversion_ratio = Column(Float, nullable=False)
    dressing_percentage = Column(Float, nullable=False)
    age_at_first_calving_months = Column(Integer, nullable=False)
    gestation_length_days = Column(Integer, nullable=False, default=283)
    calving_interval_days = Column(Integer, nullable=False)
    longevity_years = Column(Integer, nullable=False)
    calving_ease_score = Column(Float, nullable=False)
    milk_production_potential_kg = Column(Float, nullable=False)

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class CashFlow(Base):
    __tablename__ = "cash_flows"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    farm_id = Column(String, ForeignKey("farms.id"), nullable=False)

    date = Column(DateTime, nullable=False)
    description = Column(String, nullable=False)
    category = Column(String, nullable=False)
    amount = Column(Float, nullable=False)  # positive=income, negative=expense
    animal_id = Column(String, nullable=True)
    transaction_type = Column(String, nullable=False)  # income, expense, investment

    # Relationships
    farm = relationship("Farm", back_populates="cash_flows")

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class FinancialMetric(Base):
    __tablename__ = "financial_metrics"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    farm_id = Column(String, ForeignKey("farms.id"), nullable=False)

    initial_investment = Column(Float, nullable=False)
    working_capital = Column(Float, nullable=False)
    annual_revenue = Column(Float, nullable=False)
    annual_costs = Column(Float, nullable=False)
    gross_profit = Column(Float, nullable=False)
    net_profit = Column(Float, nullable=False)
    ebitda = Column(Float, nullable=False)
    return_on_investment = Column(Float, nullable=False)
    payback_period_years = Column(Float, nullable=False)
    net_present_value = Column(Float, nullable=False)
    internal_rate_of_return = Column(Float, nullable=False)
    break_even_point_animals = Column(Integer, nullable=False)
    cost_per_kg_live_weight = Column(Float, nullable=False)
    revenue_per_kg_live_weight = Column(Float, nullable=False)

    calculation_date = Column(DateTime, default=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class NutritionalRequirement(Base):
    __tablename__ = "nutritional_requirements"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # Animal characteristics
    breed = Column(String, nullable=False)
    gender = Column(String, nullable=False)
    weight_kg = Column(Float, nullable=False)
    age_months = Column(Integer, nullable=False)
    body_condition_score = Column(Float, nullable=False)
    is_pregnant = Column(Boolean, default=False)
    is_lactating = Column(Boolean, default=False)
    activity_level = Column(String, nullable=False, default="moderate")  # low, moderate, high

    # Environmental factors
    temperature_celsius = Column(Float, nullable=False, default=20.0)
    humidity_percentage = Column(Float, nullable=False, default=60.0)

    # Calculated requirements (per day)
    dry_matter_intake_kg = Column(Float, nullable=False)
    metabolizable_energy_mcal = Column(Float, nullable=False)
    crude_protein_kg = Column(Float, nullable=False)
    calcium_kg = Column(Float, nullable=False)
    phosphorus_kg = Column(Float, nullable=False)

    # NRC-based calculations
    net_energy_maintenance_mcal = Column(Float, nullable=False)
    net_energy_gain_mcal = Column(Float, nullable=False)
    rumen_degradable_protein_kg = Column(Float, nullable=False)
    rumen_undegradable_protein_kg = Column(Float, nullable=False)

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Ration(Base):
    __tablename__ = "rations"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    farm_id = Column(String, ForeignKey("farms.id"), nullable=False)
    animal_id = Column(String, ForeignKey("animals.id"), nullable=True)  # Null ise grup rasyonu

    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    ration_type = Column(String, nullable=False)  # individual, group, template
    target_group = Column(String, nullable=True)  # calves, heifers, lactating_cows, dry_cows, bulls

    # Calculated totals
    total_cost_per_day = Column(Float, nullable=False)
    total_dry_matter_kg = Column(Float, nullable=False)
    total_crude_protein_percentage = Column(Float, nullable=False)
    total_metabolizable_energy_mcal = Column(Float, nullable=False)
    total_calcium_percentage = Column(Float, nullable=False)
    total_phosphorus_percentage = Column(Float, nullable=False)

    # Optimization results
    is_optimized = Column(Boolean, default=False)
    optimization_objective = Column(String, nullable=True)  # cost, performance, balanced
    optimization_score = Column(Float, nullable=True)

    # Status
    is_active = Column(Boolean, default=True)

    # Relationships
    farm = relationship("Farm", back_populates="rations")
    animal = relationship("Animal", back_populates="rations")
    components = relationship("RationComponent", back_populates="ration", cascade="all, delete-orphan")

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class RationComponent(Base):
    __tablename__ = "ration_components"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    ration_id = Column(String, ForeignKey("rations.id"), nullable=False)
    feed_id = Column(String, ForeignKey("feeds.id"), nullable=False)

    # Amount and percentage
    amount_kg_per_day = Column(Float, nullable=False)
    percentage_of_total_dm = Column(Float, nullable=False)  # Dry matter percentage

    # Calculated nutritional contribution
    dry_matter_contribution_kg = Column(Float, nullable=False)
    protein_contribution_kg = Column(Float, nullable=False)
    energy_contribution_mcal = Column(Float, nullable=False)
    calcium_contribution_kg = Column(Float, nullable=False)
    phosphorus_contribution_kg = Column(Float, nullable=False)
    cost_contribution = Column(Float, nullable=False)

    # Relationships
    ration = relationship("Ration", back_populates="components")
    feed = relationship("Feed")

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)