{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Farm, FarmCreate, Animal, AnimalCreate, AnimalStats, Feed } from '@/types';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Farm API\nexport const farmApi = {\n  // Tüm çiftlikleri getir\n  getFarms: async (): Promise<Farm[]> => {\n    const response = await api.get('/farms/');\n    return response.data;\n  },\n\n  // Belirli bir çiftliği getir\n  getFarm: async (farmId: string): Promise<Farm> => {\n    const response = await api.get(`/farms/${farmId}`);\n    return response.data;\n  },\n\n  // Yeni çiftlik oluştur\n  createFarm: async (farmData: FarmCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/farms/', farmData);\n    return response.data;\n  },\n\n  // Çiftlik güncelle\n  updateFarm: async (farmId: string, farmData: FarmCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/farms/${farmId}`, farmData);\n    return response.data;\n  },\n\n  // Çiftlik sil\n  deleteFarm: async (farmId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/farms/${farmId}`);\n    return response.data;\n  },\n};\n\n// Animal API\nexport const animalApi = {\n  // Çiftlikteki hayvanları getir\n  getAnimalsByFarm: async (farmId: string): Promise<Animal[]> => {\n    const response = await api.get(`/animals/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir hayvanı getir\n  getAnimal: async (animalId: string): Promise<Animal> => {\n    const response = await api.get(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Yeni hayvan ekle\n  createAnimal: async (animalData: AnimalCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/animals/', animalData);\n    return response.data;\n  },\n\n  // Hayvan güncelle\n  updateAnimal: async (animalId: string, animalData: AnimalCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/animals/${animalId}`, animalData);\n    return response.data;\n  },\n\n  // Hayvan sil\n  deleteAnimal: async (animalId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftlik hayvan istatistikleri\n  getFarmAnimalStats: async (farmId: string): Promise<AnimalStats> => {\n    const response = await api.get(`/animals/farm/${farmId}/stats`);\n    return response.data;\n  },\n};\n\n// Feed API\nexport const feedApi = {\n  // Çiftlikteki yemleri getir\n  getFeedsByFarm: async (farmId: string): Promise<Feed[]> => {\n    const response = await api.get(`/feeds/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir yemi getir\n  getFeed: async (feedId: string): Promise<Feed> => {\n    const response = await api.get(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yeni yem ekle\n  createFeed: async (feedData: any): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/feeds/', feedData);\n    return response.data;\n  },\n\n  // Yem güncelle\n  updateFeed: async (feedId: string, feedData: any): Promise<{ message: string }> => {\n    const response = await api.put(`/feeds/${feedId}`, feedData);\n    return response.data;\n  },\n\n  // Yem sil\n  deleteFeed: async (feedId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yem türlerini getir\n  getFeedTypesByFarm: async (farmId: string): Promise<Record<string, any[]>> => {\n    const response = await api.get(`/feeds/farm/${farmId}/types`);\n    return response.data;\n  },\n\n  // Örnek yem verileri ekle\n  addSampleFeeds: async (farmId: string): Promise<{ message: string; feeds: string[] }> => {\n    const response = await api.post(`/feeds/farm/${farmId}/sample-feeds`);\n    return response.data;\n  },\n};\n\n// Ration API\nexport const rationApi = {\n  // Besin ihtiyaçlarını hesapla\n  calculateRequirements: async (animalId: string): Promise<any> => {\n    const response = await api.post(`/rations/calculate-requirements?animal_id=${animalId}`);\n    return response.data;\n  },\n\n  // Rasyon optimizasyonu yap\n  optimizeRation: async (request: any): Promise<any> => {\n    const response = await api.post('/rations/optimize', request);\n    return response.data;\n  },\n\n  // Çiftlikteki rasyonları getir\n  getRationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/rations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Rasyon detaylarını getir\n  getRationDetails: async (rationId: string): Promise<any> => {\n    const response = await api.get(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Rasyonu aktif hale getir\n  activateRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.put(`/rations/${rationId}/activate`);\n    return response.data;\n  },\n\n  // Rasyonu sil\n  deleteRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Hayvan için aktif rasyonu getir\n  getActiveRationForAnimal: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/rations/animal/${animalId}/active`);\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthApi = {\n  checkHealth: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health', { baseURL: 'http://localhost:8000' });\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAGA,MAAM,eAAe;AAErB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,UAAU;IACrB,wBAAwB;IACxB,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,cAAc,OAAO,UAAkB;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;QACpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,8BAA8B;IAC9B,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,0CAA0C,EAAE,UAAU;QACvF,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,kCAAkC;IAClC,0BAA0B,OAAO;QAC/B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC;QACnE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,WAAW;YAAE,SAAS;QAAwB;QAC7E,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/app/rations/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter, useParams } from 'next/navigation';\nimport { \n  <PERSON>L<PERSON><PERSON>, \n  Calculator, \n  DollarSign, \n  TrendingUp, \n  CheckCircle,\n  AlertCircle,\n  Target,\n  BarChart3,\n  <PERSON><PERSON><PERSON>,\n  Calendar,\n  Cow,\n  MapPin\n} from 'lucide-react';\nimport { rationApi } from '@/services/api';\n\ninterface RationDetails {\n  ration: {\n    id: string;\n    name: string;\n    description?: string;\n    ration_type: string;\n    is_optimized: boolean;\n    optimization_objective?: string;\n    is_active: boolean;\n    created_at: string;\n  };\n  farm_info: {\n    name: string;\n    location: string;\n  };\n  animal_info?: {\n    breed: string;\n    gender: string;\n    current_weight_kg: number;\n    age_months: number;\n    body_condition_score: number;\n    is_pregnant: boolean;\n  };\n  components: Array<{\n    id: string;\n    feed_name: string;\n    feed_type: string;\n    amount_kg_per_day: number;\n    percentage_of_total_dm: number;\n    dry_matter_contribution_kg: number;\n    protein_contribution_kg: number;\n    energy_contribution_mcal: number;\n    calcium_contribution_kg: number;\n    phosphorus_contribution_kg: number;\n    cost_contribution: number;\n    feed_details: {\n      cost_per_kg: number;\n      dry_matter_percentage: number;\n      crude_protein_percentage: number;\n      metabolizable_energy_mcal_kg: number;\n      calcium_percentage: number;\n      phosphorus_percentage: number;\n    };\n  }>;\n  nutritional_analysis: {\n    dry_matter_kg: number;\n    crude_protein_kg: number;\n    metabolizable_energy_mcal: number;\n    calcium_kg: number;\n    phosphorus_kg: number;\n    cost_per_day: number;\n    cost_per_kg_dm: number;\n    protein_percentage: number;\n    energy_density_mcal_kg: number;\n  };\n  cost_analysis: {\n    daily_cost: number;\n    weekly_cost: number;\n    monthly_cost: number;\n    yearly_cost: number;\n  };\n  summary: {\n    total_components: number;\n    concentrate_percentage: number;\n    forage_percentage: number;\n    most_expensive_component?: string;\n    protein_source?: string;\n  };\n}\n\nexport default function RationDetailPage() {\n  const router = useRouter();\n  const params = useParams();\n  const rationId = params.id as string;\n  \n  const [details, setDetails] = useState<RationDetails | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n\n  useEffect(() => {\n    if (rationId) {\n      loadRationDetails();\n    }\n  }, [rationId]);\n\n  const loadRationDetails = async () => {\n    setLoading(true);\n    try {\n      const data = await rationApi.getRationDetails(rationId);\n      setDetails(data);\n    } catch (err: any) {\n      setError(err.response?.data?.detail || 'Rasyon detayları yüklenirken hata oluştu');\n      console.error(err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('tr-TR', {\n      style: 'currency',\n      currency: 'TRY'\n    }).format(amount);\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('tr-TR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const getFeedTypeLabel = (type: string) => {\n    const labels: { [key: string]: string } = {\n      'concentrate': 'Konsantre',\n      'hay': 'Kuru Ot',\n      'silage': 'Silaj',\n      'pasture': 'Mera',\n      'supplement': 'Katkı'\n    };\n    return labels[type] || type;\n  };\n\n  const getFeedTypeColor = (type: string) => {\n    const colors: { [key: string]: string } = {\n      'concentrate': 'bg-orange-100 text-orange-800',\n      'hay': 'bg-yellow-100 text-yellow-800',\n      'silage': 'bg-green-100 text-green-800',\n      'pasture': 'bg-blue-100 text-blue-800',\n      'supplement': 'bg-purple-100 text-purple-800'\n    };\n    return colors[type] || 'bg-gray-100 text-gray-800';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Rasyon detayları yükleniyor...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={() => router.back()}\n            className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n          >\n            <ArrowLeft className=\"h-5 w-5 text-gray-600\" />\n          </button>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Rasyon Detayları</h1>\n        </div>\n        \n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n          <div className=\"flex\">\n            <AlertCircle className=\"h-5 w-5 text-red-400\" />\n            <div className=\"ml-3\">\n              <p className=\"text-sm text-red-800\">{error}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!details) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={() => router.back()}\n            className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n          >\n            <ArrowLeft className=\"h-5 w-5 text-gray-600\" />\n          </button>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Rasyon Bulunamadı</h1>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={() => router.back()}\n            className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n          >\n            <ArrowLeft className=\"h-5 w-5 text-gray-600\" />\n          </button>\n          <div>\n            <div className=\"flex items-center space-x-3\">\n              <h1 className=\"text-3xl font-bold text-gray-900\">{details.ration.name}</h1>\n              {details.ration.is_active && (\n                <CheckCircle className=\"h-6 w-6 text-green-500\" />\n              )}\n              {details.ration.is_optimized && (\n                <div className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium\">\n                  Optimize Edilmiş\n                </div>\n              )}\n            </div>\n            <p className=\"text-gray-600 mt-1\">\n              {details.ration.description || 'Açıklama bulunmuyor'}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Info Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        {/* Çiftlik Bilgisi */}\n        <div className=\"content-overlay p-4\">\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <MapPin className=\"h-4 w-4 text-gray-500\" />\n            <span className=\"text-sm font-medium text-gray-700\">Çiftlik</span>\n          </div>\n          <p className=\"font-semibold text-gray-900\">{details.farm_info.name}</p>\n          <p className=\"text-sm text-gray-600\">{details.farm_info.location}</p>\n        </div>\n\n        {/* Hayvan Bilgisi */}\n        {details.animal_info && (\n          <div className=\"content-overlay p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Cow className=\"h-4 w-4 text-gray-500\" />\n              <span className=\"text-sm font-medium text-gray-700\">Hayvan</span>\n            </div>\n            <p className=\"font-semibold text-gray-900\">\n              {details.animal_info.breed} - {details.animal_info.gender === 'male' ? 'Erkek' : 'Dişi'}\n            </p>\n            <p className=\"text-sm text-gray-600\">\n              {details.animal_info.current_weight_kg} kg - {details.animal_info.age_months} ay\n            </p>\n            <p className=\"text-xs text-gray-500\">\n              VKS: {details.animal_info.body_condition_score}\n              {details.animal_info.is_pregnant && ' - Gebe'}\n            </p>\n          </div>\n        )}\n\n        {/* Günlük Maliyet */}\n        <div className=\"content-overlay p-4\">\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <DollarSign className=\"h-4 w-4 text-gray-500\" />\n            <span className=\"text-sm font-medium text-gray-700\">Günlük Maliyet</span>\n          </div>\n          <p className=\"font-semibold text-gray-900 text-lg\">\n            {formatCurrency(details.cost_analysis.daily_cost)}\n          </p>\n          <p className=\"text-sm text-gray-600\">\n            {formatCurrency(details.nutritional_analysis.cost_per_kg_dm)}/kg KM\n          </p>\n        </div>\n\n        {/* Oluşturma Tarihi */}\n        <div className=\"content-overlay p-4\">\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <Calendar className=\"h-4 w-4 text-gray-500\" />\n            <span className=\"text-sm font-medium text-gray-700\">Oluşturulma</span>\n          </div>\n          <p className=\"font-semibold text-gray-900\">\n            {formatDate(details.ration.created_at)}\n          </p>\n          <p className=\"text-sm text-gray-600\">\n            {details.ration.ration_type === 'individual' ? 'Bireysel' : \n             details.ration.ration_type === 'group' ? 'Grup' : 'Şablon'}\n          </p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Sol Kolon - Besin Analizi */}\n        <div className=\"space-y-6\">\n          {/* Besin Değeri Analizi */}\n          <div className=\"content-overlay p-6\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <BarChart3 className=\"h-5 w-5 text-green-600\" />\n              <h2 className=\"text-lg font-semibold text-gray-900\">Besin Değeri Analizi</h2>\n            </div>\n            \n            <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"bg-gray-50 p-3 rounded-lg\">\n                  <p className=\"text-sm text-gray-600\">Kuru Madde</p>\n                  <p className=\"font-semibold text-gray-900\">\n                    {details.nutritional_analysis.dry_matter_kg.toFixed(1)} kg/gün\n                  </p>\n                </div>\n                <div className=\"bg-gray-50 p-3 rounded-lg\">\n                  <p className=\"text-sm text-gray-600\">Ham Protein</p>\n                  <p className=\"font-semibold text-gray-900\">\n                    {details.nutritional_analysis.protein_percentage.toFixed(1)}%\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    ({(details.nutritional_analysis.crude_protein_kg * 1000).toFixed(0)} g/gün)\n                  </p>\n                </div>\n                <div className=\"bg-gray-50 p-3 rounded-lg\">\n                  <p className=\"text-sm text-gray-600\">Metabolik Enerji</p>\n                  <p className=\"font-semibold text-gray-900\">\n                    {details.nutritional_analysis.metabolizable_energy_mcal.toFixed(1)} Mcal/gün\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    ({details.nutritional_analysis.energy_density_mcal_kg.toFixed(1)} Mcal/kg KM)\n                  </p>\n                </div>\n                <div className=\"bg-gray-50 p-3 rounded-lg\">\n                  <p className=\"text-sm text-gray-600\">Kalsiyum</p>\n                  <p className=\"font-semibold text-gray-900\">\n                    {(details.nutritional_analysis.calcium_kg * 1000).toFixed(1)} g/gün\n                  </p>\n                </div>\n                <div className=\"bg-gray-50 p-3 rounded-lg\">\n                  <p className=\"text-sm text-gray-600\">Fosfor</p>\n                  <p className=\"font-semibold text-gray-900\">\n                    {(details.nutritional_analysis.phosphorus_kg * 1000).toFixed(1)} g/gün\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Maliyet Analizi */}\n          <div className=\"content-overlay p-6\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <TrendingUp className=\"h-5 w-5 text-blue-600\" />\n              <h2 className=\"text-lg font-semibold text-gray-900\">Maliyet Analizi</h2>\n            </div>\n            \n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Günlük:</span>\n                <span className=\"font-semibold text-gray-900\">\n                  {formatCurrency(details.cost_analysis.daily_cost)}\n                </span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Haftalık:</span>\n                <span className=\"font-semibold text-gray-900\">\n                  {formatCurrency(details.cost_analysis.weekly_cost)}\n                </span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Aylık:</span>\n                <span className=\"font-semibold text-gray-900\">\n                  {formatCurrency(details.cost_analysis.monthly_cost)}\n                </span>\n              </div>\n              <div className=\"flex justify-between items-center border-t pt-3\">\n                <span className=\"text-gray-600 font-medium\">Yıllık:</span>\n                <span className=\"font-bold text-gray-900 text-lg\">\n                  {formatCurrency(details.cost_analysis.yearly_cost)}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Sağ Kolon - Bileşenler ve Özet */}\n        <div className=\"space-y-6\">\n          {/* Rasyon Özeti */}\n          <div className=\"content-overlay p-6\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <PieChart className=\"h-5 w-5 text-purple-600\" />\n              <h2 className=\"text-lg font-semibold text-gray-900\">Rasyon Özeti</h2>\n            </div>\n            \n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Toplam Bileşen:</span>\n                <span className=\"font-semibold text-gray-900\">{details.summary.total_components}</span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Konsantre Oranı:</span>\n                <span className=\"font-semibold text-gray-900\">\n                  %{details.summary.concentrate_percentage.toFixed(1)}\n                </span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Kaba Yem Oranı:</span>\n                <span className=\"font-semibold text-gray-900\">\n                  %{details.summary.forage_percentage.toFixed(1)}\n                </span>\n              </div>\n              {details.summary.most_expensive_component && (\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-gray-600\">En Pahalı Bileşen:</span>\n                  <span className=\"font-semibold text-gray-900 text-sm\">\n                    {details.summary.most_expensive_component}\n                  </span>\n                </div>\n              )}\n              {details.summary.protein_source && (\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-gray-600\">Ana Protein Kaynağı:</span>\n                  <span className=\"font-semibold text-gray-900 text-sm\">\n                    {details.summary.protein_source}\n                  </span>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Rasyon Bileşenleri */}\n          <div className=\"content-overlay p-6\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <Target className=\"h-5 w-5 text-orange-600\" />\n              <h2 className=\"text-lg font-semibold text-gray-900\">Rasyon Bileşenleri</h2>\n            </div>\n            \n            <div className=\"space-y-4\">\n              {details.components.map((component) => (\n                <div key={component.id} className=\"border border-gray-200 rounded-lg p-4\">\n                  <div className=\"flex justify-between items-start mb-2\">\n                    <div>\n                      <h3 className=\"font-semibold text-gray-900\">{component.feed_name}</h3>\n                      <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getFeedTypeColor(component.feed_type)}`}>\n                        {getFeedTypeLabel(component.feed_type)}\n                      </span>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"font-semibold text-gray-900\">\n                        {component.amount_kg_per_day.toFixed(1)} kg/gün\n                      </p>\n                      <p className=\"text-sm text-gray-600\">\n                        %{component.percentage_of_total_dm.toFixed(1)} KM\n                      </p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"grid grid-cols-2 gap-2 text-xs text-gray-600\">\n                    <div>Protein: {(component.protein_contribution_kg * 1000).toFixed(0)}g</div>\n                    <div>Enerji: {component.energy_contribution_mcal.toFixed(1)} Mcal</div>\n                    <div>Kalsiyum: {(component.calcium_contribution_kg * 1000).toFixed(1)}g</div>\n                    <div>Fosfor: {(component.phosphorus_contribution_kg * 1000).toFixed(1)}g</div>\n                  </div>\n                  \n                  <div className=\"mt-2 pt-2 border-t border-gray-100\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-gray-600\">Günlük Maliyet:</span>\n                      <span className=\"font-semibold text-gray-900\">\n                        {formatCurrency(component.cost_contribution)}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;AAlBA;;;;;AA0Fe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,OAAO,EAAE;IAE1B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,UAAU;gBACZ;YACF;QACF;qCAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB;QACxB,WAAW;QACX,IAAI;YACF,MAAM,OAAO,MAAM,yHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;YAC9C,WAAW;QACb,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,UAAU;YACvC,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAoC;YACxC,eAAe;YACf,OAAO;YACP,UAAU;YACV,WAAW;YACX,cAAc;QAChB;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAoC;YACxC,eAAe;YACf,OAAO;YACP,UAAU;YACV,WAAW;YACX,cAAc;QAChB;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,OAAO,IAAI;4BAC1B,WAAU;sCAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;;;;;;;8BAGnD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMjD;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI;wBAC1B,WAAU;kCAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIzD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,OAAO,IAAI;4BAC1B,WAAU;sCAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoC,QAAQ,MAAM,CAAC,IAAI;;;;;;wCACpE,QAAQ,MAAM,CAAC,SAAS,kBACvB,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAExB,QAAQ,MAAM,CAAC,YAAY,kBAC1B,6LAAC;4CAAI,WAAU;sDAAuE;;;;;;;;;;;;8CAK1F,6LAAC;oCAAE,WAAU;8CACV,QAAQ,MAAM,CAAC,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAE,WAAU;0CAA+B,QAAQ,SAAS,CAAC,IAAI;;;;;;0CAClE,6LAAC;gCAAE,WAAU;0CAAyB,QAAQ,SAAS,CAAC,QAAQ;;;;;;;;;;;;oBAIjE,QAAQ,WAAW,kBAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qLAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAE,WAAU;;oCACV,QAAQ,WAAW,CAAC,KAAK;oCAAC;oCAAI,QAAQ,WAAW,CAAC,MAAM,KAAK,SAAS,UAAU;;;;;;;0CAEnF,6LAAC;gCAAE,WAAU;;oCACV,QAAQ,WAAW,CAAC,iBAAiB;oCAAC;oCAAO,QAAQ,WAAW,CAAC,UAAU;oCAAC;;;;;;;0CAE/E,6LAAC;gCAAE,WAAU;;oCAAwB;oCAC7B,QAAQ,WAAW,CAAC,oBAAoB;oCAC7C,QAAQ,WAAW,CAAC,WAAW,IAAI;;;;;;;;;;;;;kCAM1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAE,WAAU;0CACV,eAAe,QAAQ,aAAa,CAAC,UAAU;;;;;;0CAElD,6LAAC;gCAAE,WAAU;;oCACV,eAAe,QAAQ,oBAAoB,CAAC,cAAc;oCAAE;;;;;;;;;;;;;kCAKjE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAE,WAAU;0CACV,WAAW,QAAQ,MAAM,CAAC,UAAU;;;;;;0CAEvC,6LAAC;gCAAE,WAAU;0CACV,QAAQ,MAAM,CAAC,WAAW,KAAK,eAAe,aAC9C,QAAQ,MAAM,CAAC,WAAW,KAAK,UAAU,SAAS;;;;;;;;;;;;;;;;;;0BAKzD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;;kDAGtD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;;gEACV,QAAQ,oBAAoB,CAAC,aAAa,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAG3D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;;gEACV,QAAQ,oBAAoB,CAAC,kBAAkB,CAAC,OAAO,CAAC;gEAAG;;;;;;;sEAE9D,6LAAC;4DAAE,WAAU;;gEAAwB;gEACjC,CAAC,QAAQ,oBAAoB,CAAC,gBAAgB,GAAG,IAAI,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAGxE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;;gEACV,QAAQ,oBAAoB,CAAC,yBAAyB,CAAC,OAAO,CAAC;gEAAG;;;;;;;sEAErE,6LAAC;4DAAE,WAAU;;gEAAwB;gEACjC,QAAQ,oBAAoB,CAAC,sBAAsB,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAGrE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;;gEACV,CAAC,QAAQ,oBAAoB,CAAC,UAAU,GAAG,IAAI,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAGjE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;;gEACV,CAAC,QAAQ,oBAAoB,CAAC,aAAa,GAAG,IAAI,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ1E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;;kDAGtD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEACb,eAAe,QAAQ,aAAa,CAAC,UAAU;;;;;;;;;;;;0DAGpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEACb,eAAe,QAAQ,aAAa,CAAC,WAAW;;;;;;;;;;;;0DAGrD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEACb,eAAe,QAAQ,aAAa,CAAC,YAAY;;;;;;;;;;;;0DAGtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA4B;;;;;;kEAC5C,6LAAC;wDAAK,WAAU;kEACb,eAAe,QAAQ,aAAa,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3D,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;;kDAGtD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAA+B,QAAQ,OAAO,CAAC,gBAAgB;;;;;;;;;;;;0DAEjF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAA8B;4DAC1C,QAAQ,OAAO,CAAC,sBAAsB,CAAC,OAAO,CAAC;;;;;;;;;;;;;0DAGrD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAA8B;4DAC1C,QAAQ,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC;;;;;;;;;;;;;4CAG/C,QAAQ,OAAO,CAAC,wBAAwB,kBACvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEACb,QAAQ,OAAO,CAAC,wBAAwB;;;;;;;;;;;;4CAI9C,QAAQ,OAAO,CAAC,cAAc,kBAC7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEACb,QAAQ,OAAO,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;0CAQzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;;kDAGtD,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAC,0BACvB,6LAAC;gDAAuB,WAAU;;kEAChC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAA+B,UAAU,SAAS;;;;;;kFAChE,6LAAC;wEAAK,WAAW,CAAC,wDAAwD,EAAE,iBAAiB,UAAU,SAAS,GAAG;kFAChH,iBAAiB,UAAU,SAAS;;;;;;;;;;;;0EAGzC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;;4EACV,UAAU,iBAAiB,CAAC,OAAO,CAAC;4EAAG;;;;;;;kFAE1C,6LAAC;wEAAE,WAAU;;4EAAwB;4EACjC,UAAU,sBAAsB,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;;;;;;kEAKpD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAI;oEAAU,CAAC,UAAU,uBAAuB,GAAG,IAAI,EAAE,OAAO,CAAC;oEAAG;;;;;;;0EACrE,6LAAC;;oEAAI;oEAAS,UAAU,wBAAwB,CAAC,OAAO,CAAC;oEAAG;;;;;;;0EAC5D,6LAAC;;oEAAI;oEAAW,CAAC,UAAU,uBAAuB,GAAG,IAAI,EAAE,OAAO,CAAC;oEAAG;;;;;;;0EACtE,6LAAC;;oEAAI;oEAAS,CAAC,UAAU,0BAA0B,GAAG,IAAI,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAGzE,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,6LAAC;oEAAK,WAAU;8EACb,eAAe,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;+CA7BzC,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCtC;GA1YwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}