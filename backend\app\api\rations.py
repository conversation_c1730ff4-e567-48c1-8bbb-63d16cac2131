"""
Rasyon yönetimi API endpoint'leri
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime

from ..database.database import get_db
from ..database import models
from ..services.ration_calculator import RationOptimizer, NRCCalculator

router = APIRouter(prefix="/rations", tags=["rations"])


class RationCreate(BaseModel):
    farm_id: str
    animal_id: Optional[str] = None
    name: str
    description: Optional[str] = None
    ration_type: str = "individual"  # individual, group, template
    target_group: Optional[str] = None


class RationComponentCreate(BaseModel):
    feed_id: str
    amount_kg_per_day: float


class RationOptimizationRequest(BaseModel):
    farm_id: str
    animal_id: str
    objective: str = "cost"  # cost, performance, balanced
    name: str
    description: Optional[str] = None


class RationResponse(BaseModel):
    id: str
    farm_id: str
    animal_id: Optional[str]
    name: str
    description: Optional[str]
    ration_type: str
    target_group: Optional[str]
    total_cost_per_day: float
    total_dry_matter_kg: float
    total_crude_protein_percentage: float
    total_metabolizable_energy_mcal: float
    is_optimized: bool
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True


class RationComponentResponse(BaseModel):
    id: str
    feed_name: str
    feed_type: str
    amount_kg_per_day: float
    percentage_of_total_dm: float
    cost_contribution: float
    
    class Config:
        from_attributes = True


class NutritionalRequirementResponse(BaseModel):
    animal_id: str
    dry_matter_intake_kg: float
    energy_requirements: Dict[str, float]
    protein_requirements: Dict[str, float]
    mineral_requirements: Dict[str, float]


@router.post("/calculate-requirements", response_model=NutritionalRequirementResponse)
def calculate_nutritional_requirements(animal_id: str, db: Session = Depends(get_db)):
    """Hayvan için besin ihtiyaçlarını hesapla"""
    try:
        optimizer = RationOptimizer(db)
        requirements = optimizer.calculate_nutritional_requirements(animal_id)
        return requirements
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Hesaplama hatası: {str(e)}")


@router.post("/optimize", response_model=dict)
def optimize_ration(request: RationOptimizationRequest, db: Session = Depends(get_db)):
    """Hayvan için rasyon optimizasyonu yap"""
    try:
        optimizer = RationOptimizer(db)
        
        # Besin ihtiyaçlarını hesapla
        requirements = optimizer.calculate_nutritional_requirements(request.animal_id)
        
        # Mevcut yemleri getir
        feeds = optimizer.get_available_feeds(request.farm_id)
        
        if not feeds:
            raise HTTPException(status_code=400, detail="Çiftlikte yem bulunamadı")
        
        # Optimizasyon yap
        optimization_result = optimizer.optimize_ration_simple(
            requirements, feeds, request.objective
        )
        
        # Veritabanına kaydet
        ration = optimizer.create_ration_record(
            request.farm_id,
            request.animal_id,
            optimization_result,
            request.name,
            request.description
        )
        
        return {
            "ration_id": ration.id,
            "message": "Rasyon başarıyla optimize edildi",
            "optimization_result": {
                "total_cost_per_day": optimization_result["totals"]["cost_per_day"],
                "total_dry_matter_kg": optimization_result["totals"]["dry_matter_kg"],
                "components": [
                    {
                        "feed_name": optimization_result["concentrate"]["feed"].name,
                        "amount_kg": optimization_result["concentrate"]["amount_kg"],
                        "cost": optimization_result["concentrate"]["nutrition"]["cost"]
                    },
                    {
                        "feed_name": optimization_result["forage"]["feed"].name,
                        "amount_kg": optimization_result["forage"]["amount_kg"],
                        "cost": optimization_result["forage"]["nutrition"]["cost"]
                    }
                ],
                "adequacy_ratios": optimization_result["ratios"]
            }
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Optimizasyon hatası: {str(e)}")


@router.get("/farm/{farm_id}", response_model=List[RationResponse])
def get_rations_by_farm(farm_id: str, db: Session = Depends(get_db)):
    """Çiftlikteki tüm rasyonları listele"""
    rations = db.query(models.Ration).filter(
        models.Ration.farm_id == farm_id,
        models.Ration.is_active == True
    ).all()
    
    return rations


@router.get("/{ration_id}")
def get_ration_details(ration_id: str, db: Session = Depends(get_db)):
    """Rasyon detaylarını getir"""
    ration = db.query(models.Ration).filter(models.Ration.id == ration_id).first()
    if not ration:
        raise HTTPException(status_code=404, detail="Rasyon bulunamadı")
    
    # Bileşenleri getir
    components = db.query(models.RationComponent).filter(
        models.RationComponent.ration_id == ration_id
    ).all()
    
    component_details = []
    for comp in components:
        feed = db.query(models.Feed).filter(models.Feed.id == comp.feed_id).first()
        component_details.append({
            "id": comp.id,
            "feed_name": feed.name if feed else "Bilinmeyen",
            "feed_type": feed.feed_type if feed else "unknown",
            "amount_kg_per_day": comp.amount_kg_per_day,
            "percentage_of_total_dm": comp.percentage_of_total_dm,
            "dry_matter_contribution_kg": comp.dry_matter_contribution_kg,
            "protein_contribution_kg": comp.protein_contribution_kg,
            "energy_contribution_mcal": comp.energy_contribution_mcal,
            "calcium_contribution_kg": comp.calcium_contribution_kg,
            "phosphorus_contribution_kg": comp.phosphorus_contribution_kg,
            "cost_contribution": comp.cost_contribution
        })
    
    return {
        "ration": {
            "id": ration.id,
            "farm_id": ration.farm_id,
            "animal_id": ration.animal_id,
            "name": ration.name,
            "description": ration.description,
            "ration_type": ration.ration_type,
            "target_group": ration.target_group,
            "total_cost_per_day": ration.total_cost_per_day,
            "total_dry_matter_kg": ration.total_dry_matter_kg,
            "total_crude_protein_percentage": ration.total_crude_protein_percentage,
            "total_metabolizable_energy_mcal": ration.total_metabolizable_energy_mcal,
            "total_calcium_percentage": ration.total_calcium_percentage,
            "total_phosphorus_percentage": ration.total_phosphorus_percentage,
            "is_optimized": ration.is_optimized,
            "optimization_objective": ration.optimization_objective,
            "optimization_score": ration.optimization_score,
            "is_active": ration.is_active,
            "created_at": ration.created_at
        },
        "components": component_details
    }


@router.put("/{ration_id}/activate")
def activate_ration(ration_id: str, db: Session = Depends(get_db)):
    """Rasyonu aktif hale getir"""
    ration = db.query(models.Ration).filter(models.Ration.id == ration_id).first()
    if not ration:
        raise HTTPException(status_code=404, detail="Rasyon bulunamadı")
    
    # Aynı hayvan için diğer rasyonları pasif yap
    if ration.animal_id:
        db.query(models.Ration).filter(
            models.Ration.animal_id == ration.animal_id,
            models.Ration.id != ration_id
        ).update({"is_active": False})
    
    ration.is_active = True
    db.commit()
    
    return {"message": "Rasyon aktif hale getirildi"}


@router.delete("/{ration_id}")
def delete_ration(ration_id: str, db: Session = Depends(get_db)):
    """Rasyonu sil"""
    ration = db.query(models.Ration).filter(models.Ration.id == ration_id).first()
    if not ration:
        raise HTTPException(status_code=404, detail="Rasyon bulunamadı")
    
    # Bileşenleri de sil (cascade ile otomatik silinir)
    db.delete(ration)
    db.commit()
    
    return {"message": "Rasyon başarıyla silindi"}


@router.get("/animal/{animal_id}/active")
def get_active_ration_for_animal(animal_id: str, db: Session = Depends(get_db)):
    """Hayvan için aktif rasyonu getir"""
    ration = db.query(models.Ration).filter(
        models.Ration.animal_id == animal_id,
        models.Ration.is_active == True
    ).first()
    
    if not ration:
        return {"message": "Aktif rasyon bulunamadı"}
    
    return get_ration_details(ration.id, db)
