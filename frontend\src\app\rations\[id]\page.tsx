'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  <PERSON>Lef<PERSON>,
  Calculator,
  DollarSign,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  Target,
  BarChart3,
  <PERSON><PERSON>hart,
  Calendar,
  Users,
  MapPin
} from 'lucide-react';
import { rationApi } from '@/services/api';

interface RationDetails {
  ration: {
    id: string;
    name: string;
    description?: string;
    ration_type: string;
    is_optimized: boolean;
    optimization_objective?: string;
    is_active: boolean;
    created_at: string;
  };
  farm_info: {
    name: string;
    location: string;
  };
  animal_info?: {
    breed: string;
    gender: string;
    current_weight_kg: number;
    age_months: number;
    body_condition_score: number;
    is_pregnant: boolean;
  };
  components: Array<{
    id: string;
    feed_name: string;
    feed_type: string;
    amount_kg_per_day: number;
    percentage_of_total_dm: number;
    dry_matter_contribution_kg: number;
    protein_contribution_kg: number;
    energy_contribution_mcal: number;
    calcium_contribution_kg: number;
    phosphorus_contribution_kg: number;
    cost_contribution: number;
    feed_details: {
      cost_per_kg: number;
      dry_matter_percentage: number;
      crude_protein_percentage: number;
      metabolizable_energy_mcal_kg: number;
      calcium_percentage: number;
      phosphorus_percentage: number;
    };
  }>;
  nutritional_analysis: {
    dry_matter_kg: number;
    crude_protein_kg: number;
    metabolizable_energy_mcal: number;
    calcium_kg: number;
    phosphorus_kg: number;
    cost_per_day: number;
    cost_per_kg_dm: number;
    protein_percentage: number;
    energy_density_mcal_kg: number;
  };
  cost_analysis: {
    daily_cost: number;
    weekly_cost: number;
    monthly_cost: number;
    yearly_cost: number;
  };
  summary: {
    total_components: number;
    concentrate_percentage: number;
    forage_percentage: number;
    most_expensive_component?: string;
    protein_source?: string;
  };
}

export default function RationDetailPage() {
  const router = useRouter();
  const params = useParams();
  const rationId = params.id as string;

  const [details, setDetails] = useState<RationDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    if (rationId) {
      loadRationDetails();
    }
  }, [rationId]);

  const loadRationDetails = async () => {
    setLoading(true);
    try {
      const data = await rationApi.getRationDetails(rationId);
      setDetails(data);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Rasyon detayları yüklenirken hata oluştu');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getFeedTypeLabel = (type: string) => {
    const labels: { [key: string]: string } = {
      'concentrate': 'Konsantre',
      'hay': 'Kuru Ot',
      'silage': 'Silaj',
      'pasture': 'Mera',
      'supplement': 'Katkı'
    };
    return labels[type] || type;
  };

  const getFeedTypeColor = (type: string) => {
    const colors: { [key: string]: string } = {
      'concentrate': 'bg-orange-100 text-orange-800',
      'hay': 'bg-yellow-100 text-yellow-800',
      'silage': 'bg-green-100 text-green-800',
      'pasture': 'bg-blue-100 text-blue-800',
      'supplement': 'bg-purple-100 text-purple-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Rasyon detayları yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </button>
          <h1 className="text-3xl font-bold text-gray-900">Rasyon Detayları</h1>
        </div>

        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!details) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </button>
          <h1 className="text-3xl font-bold text-gray-900">Rasyon Bulunamadı</h1>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </button>
          <div>
            <div className="flex items-center space-x-3">
              <h1 className="text-3xl font-bold text-gray-900">{details.ration.name}</h1>
              {details.ration.is_active && (
                <CheckCircle className="h-6 w-6 text-green-500" />
              )}
              {details.ration.is_optimized && (
                <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                  Optimize Edilmiş
                </div>
              )}
            </div>
            <p className="text-gray-600 mt-1">
              {details.ration.description || 'Açıklama bulunmuyor'}
            </p>
          </div>
        </div>
      </div>

      {/* Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Çiftlik Bilgisi */}
        <div className="content-overlay p-4">
          <div className="flex items-center space-x-2 mb-2">
            <MapPin className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Çiftlik</span>
          </div>
          <p className="font-semibold text-gray-900">{details.farm_info.name}</p>
          <p className="text-sm text-gray-600">{details.farm_info.location}</p>
        </div>

        {/* Hayvan Bilgisi */}
        {details.animal_info && (
          <div className="content-overlay p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Users className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Hayvan</span>
            </div>
            <p className="font-semibold text-gray-900">
              {details.animal_info.breed} - {details.animal_info.gender === 'male' ? 'Erkek' : 'Dişi'}
            </p>
            <p className="text-sm text-gray-600">
              {details.animal_info.current_weight_kg} kg - {details.animal_info.age_months} ay
            </p>
            <p className="text-xs text-gray-500">
              VKS: {details.animal_info.body_condition_score}
              {details.animal_info.is_pregnant && ' - Gebe'}
            </p>
          </div>
        )}

        {/* Günlük Maliyet */}
        <div className="content-overlay p-4">
          <div className="flex items-center space-x-2 mb-2">
            <DollarSign className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Günlük Maliyet</span>
          </div>
          <p className="font-semibold text-gray-900 text-lg">
            {formatCurrency(details.cost_analysis.daily_cost)}
          </p>
          <p className="text-sm text-gray-600">
            {formatCurrency(details.nutritional_analysis.cost_per_kg_dm)}/kg KM
          </p>
        </div>

        {/* Oluşturma Tarihi */}
        <div className="content-overlay p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Calendar className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Oluşturulma</span>
          </div>
          <p className="font-semibold text-gray-900">
            {formatDate(details.ration.created_at)}
          </p>
          <p className="text-sm text-gray-600">
            {details.ration.ration_type === 'individual' ? 'Bireysel' :
             details.ration.ration_type === 'group' ? 'Grup' : 'Şablon'}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sol Kolon - Besin Analizi */}
        <div className="space-y-6">
          {/* Besin Değeri Analizi */}
          <div className="content-overlay p-6">
            <div className="flex items-center space-x-2 mb-4">
              <BarChart3 className="h-5 w-5 text-green-600" />
              <h2 className="text-lg font-semibold text-gray-900">Besin Değeri Analizi</h2>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-sm text-gray-600">Kuru Madde</p>
                  <p className="font-semibold text-gray-900">
                    {details.nutritional_analysis.dry_matter_kg.toFixed(1)} kg/gün
                  </p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-sm text-gray-600">Ham Protein</p>
                  <p className="font-semibold text-gray-900">
                    {details.nutritional_analysis.protein_percentage.toFixed(1)}%
                  </p>
                  <p className="text-xs text-gray-500">
                    ({(details.nutritional_analysis.crude_protein_kg * 1000).toFixed(0)} g/gün)
                  </p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-sm text-gray-600">Metabolik Enerji</p>
                  <p className="font-semibold text-gray-900">
                    {details.nutritional_analysis.metabolizable_energy_mcal.toFixed(1)} Mcal/gün
                  </p>
                  <p className="text-xs text-gray-500">
                    ({details.nutritional_analysis.energy_density_mcal_kg.toFixed(1)} Mcal/kg KM)
                  </p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-sm text-gray-600">Kalsiyum</p>
                  <p className="font-semibold text-gray-900">
                    {(details.nutritional_analysis.calcium_kg * 1000).toFixed(1)} g/gün
                  </p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-sm text-gray-600">Fosfor</p>
                  <p className="font-semibold text-gray-900">
                    {(details.nutritional_analysis.phosphorus_kg * 1000).toFixed(1)} g/gün
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Maliyet Analizi */}
          <div className="content-overlay p-6">
            <div className="flex items-center space-x-2 mb-4">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <h2 className="text-lg font-semibold text-gray-900">Maliyet Analizi</h2>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Günlük:</span>
                <span className="font-semibold text-gray-900">
                  {formatCurrency(details.cost_analysis.daily_cost)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Haftalık:</span>
                <span className="font-semibold text-gray-900">
                  {formatCurrency(details.cost_analysis.weekly_cost)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Aylık:</span>
                <span className="font-semibold text-gray-900">
                  {formatCurrency(details.cost_analysis.monthly_cost)}
                </span>
              </div>
              <div className="flex justify-between items-center border-t pt-3">
                <span className="text-gray-600 font-medium">Yıllık:</span>
                <span className="font-bold text-gray-900 text-lg">
                  {formatCurrency(details.cost_analysis.yearly_cost)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Sağ Kolon - Bileşenler ve Özet */}
        <div className="space-y-6">
          {/* Rasyon Özeti */}
          <div className="content-overlay p-6">
            <div className="flex items-center space-x-2 mb-4">
              <PieChart className="h-5 w-5 text-purple-600" />
              <h2 className="text-lg font-semibold text-gray-900">Rasyon Özeti</h2>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Toplam Bileşen:</span>
                <span className="font-semibold text-gray-900">{details.summary.total_components}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Konsantre Oranı:</span>
                <span className="font-semibold text-gray-900">
                  %{details.summary.concentrate_percentage.toFixed(1)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Kaba Yem Oranı:</span>
                <span className="font-semibold text-gray-900">
                  %{details.summary.forage_percentage.toFixed(1)}
                </span>
              </div>
              {details.summary.most_expensive_component && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">En Pahalı Bileşen:</span>
                  <span className="font-semibold text-gray-900 text-sm">
                    {details.summary.most_expensive_component}
                  </span>
                </div>
              )}
              {details.summary.protein_source && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Ana Protein Kaynağı:</span>
                  <span className="font-semibold text-gray-900 text-sm">
                    {details.summary.protein_source}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Rasyon Bileşenleri */}
          <div className="content-overlay p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Target className="h-5 w-5 text-orange-600" />
              <h2 className="text-lg font-semibold text-gray-900">Rasyon Bileşenleri</h2>
            </div>

            <div className="space-y-4">
              {details.components.map((component) => (
                <div key={component.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="font-semibold text-gray-900">{component.feed_name}</h3>
                      <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getFeedTypeColor(component.feed_type)}`}>
                        {getFeedTypeLabel(component.feed_type)}
                      </span>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">
                        {component.amount_kg_per_day.toFixed(1)} kg/gün
                      </p>
                      <p className="text-sm text-gray-600">
                        %{component.percentage_of_total_dm.toFixed(1)} KM
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                    <div>Protein: {(component.protein_contribution_kg * 1000).toFixed(0)}g</div>
                    <div>Enerji: {component.energy_contribution_mcal.toFixed(1)} Mcal</div>
                    <div>Kalsiyum: {(component.calcium_contribution_kg * 1000).toFixed(1)}g</div>
                    <div>Fosfor: {(component.phosphorus_contribution_kg * 1000).toFixed(1)}g</div>
                  </div>

                  <div className="mt-2 pt-2 border-t border-gray-100">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Günlük Maliyet:</span>
                      <span className="font-semibold text-gray-900">
                        {formatCurrency(component.cost_contribution)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
