{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Farm, FarmCreate, Animal, AnimalCreate, AnimalStats, Feed } from '@/types';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Farm API\nexport const farmApi = {\n  // Tüm çiftlikleri getir\n  getFarms: async (): Promise<Farm[]> => {\n    const response = await api.get('/farms/');\n    return response.data;\n  },\n\n  // Belirli bir çiftliği getir\n  getFarm: async (farmId: string): Promise<Farm> => {\n    const response = await api.get(`/farms/${farmId}`);\n    return response.data;\n  },\n\n  // Yeni çiftlik oluştur\n  createFarm: async (farmData: FarmCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/farms/', farmData);\n    return response.data;\n  },\n\n  // Çiftlik güncelle\n  updateFarm: async (farmId: string, farmData: FarmCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/farms/${farmId}`, farmData);\n    return response.data;\n  },\n\n  // Çiftlik sil\n  deleteFarm: async (farmId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/farms/${farmId}`);\n    return response.data;\n  },\n};\n\n// Animal API\nexport const animalApi = {\n  // Çiftlikteki hayvanları getir\n  getAnimalsByFarm: async (farmId: string): Promise<Animal[]> => {\n    const response = await api.get(`/animals/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir hayvanı getir\n  getAnimal: async (animalId: string): Promise<Animal> => {\n    const response = await api.get(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Yeni hayvan ekle\n  createAnimal: async (animalData: AnimalCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/animals/', animalData);\n    return response.data;\n  },\n\n  // Hayvan güncelle\n  updateAnimal: async (animalId: string, animalData: AnimalCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/animals/${animalId}`, animalData);\n    return response.data;\n  },\n\n  // Hayvan sil\n  deleteAnimal: async (animalId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftlik hayvan istatistikleri\n  getFarmAnimalStats: async (farmId: string): Promise<AnimalStats> => {\n    const response = await api.get(`/animals/farm/${farmId}/stats`);\n    return response.data;\n  },\n};\n\n// Feed API\nexport const feedApi = {\n  // Çiftlikteki yemleri getir\n  getFeedsByFarm: async (farmId: string): Promise<Feed[]> => {\n    const response = await api.get(`/feeds/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir yemi getir\n  getFeed: async (feedId: string): Promise<Feed> => {\n    const response = await api.get(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yeni yem ekle\n  createFeed: async (feedData: any): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/feeds/', feedData);\n    return response.data;\n  },\n\n  // Yem güncelle\n  updateFeed: async (feedId: string, feedData: any): Promise<{ message: string }> => {\n    const response = await api.put(`/feeds/${feedId}`, feedData);\n    return response.data;\n  },\n\n  // Yem sil\n  deleteFeed: async (feedId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yem türlerini getir\n  getFeedTypesByFarm: async (farmId: string): Promise<Record<string, any[]>> => {\n    const response = await api.get(`/feeds/farm/${farmId}/types`);\n    return response.data;\n  },\n\n  // Örnek yem verileri ekle\n  addSampleFeeds: async (farmId: string): Promise<{ message: string; feeds: string[] }> => {\n    const response = await api.post(`/feeds/farm/${farmId}/sample-feeds`);\n    return response.data;\n  },\n};\n\n// Ration API\nexport const rationApi = {\n  // Besin ihtiyaçlarını hesapla\n  calculateRequirements: async (animalId: string): Promise<any> => {\n    const response = await api.post(`/rations/calculate-requirements?animal_id=${animalId}`);\n    return response.data;\n  },\n\n  // Rasyon optimizasyonu yap\n  optimizeRation: async (request: any): Promise<any> => {\n    const response = await api.post('/rations/optimize', request);\n    return response.data;\n  },\n\n  // Çiftlikteki rasyonları getir\n  getRationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/rations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Rasyon detaylarını getir\n  getRationDetails: async (rationId: string): Promise<any> => {\n    const response = await api.get(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Rasyonu aktif hale getir\n  activateRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.put(`/rations/${rationId}/activate`);\n    return response.data;\n  },\n\n  // Rasyonu sil\n  deleteRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Hayvan için aktif rasyonu getir\n  getActiveRationForAnimal: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/rations/animal/${animalId}/active`);\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthApi = {\n  checkHealth: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health', { baseURL: 'http://localhost:8000' });\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAGA,MAAM,eAAe;AAErB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,UAAU;IACrB,wBAAwB;IACxB,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,cAAc,OAAO,UAAkB;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;QACpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,8BAA8B;IAC9B,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,0CAA0C,EAAE,UAAU;QACvF,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,kCAAkC;IAClC,0BAA0B,OAAO;QAC/B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC;QACnE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,WAAW;YAAE,SAAS;QAAwB;QAC7E,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/app/rations/optimize/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { \n  Calculator, \n  ArrowLeft, \n  AlertCircle, \n  CheckCircle,\n  TrendingUp,\n  DollarSign,\n  Target\n} from 'lucide-react';\nimport { rationApi, farmApi, animalApi } from '@/services/api';\nimport { Farm, Animal, NutritionalRequirement, OptimizationResult } from '@/types';\n\nexport default function OptimizeRationPage() {\n  const router = useRouter();\n  const [farms, setFarms] = useState<Farm[]>([]);\n  const [animals, setAnimals] = useState<Animal[]>([]);\n  const [selectedFarmId, setSelectedFarmId] = useState<string>('');\n  const [selectedAnimalId, setSelectedAnimalId] = useState<string>('');\n  const [rationName, setRationName] = useState<string>('');\n  const [rationDescription, setRationDescription] = useState<string>('');\n  const [objective, setObjective] = useState<'cost' | 'performance' | 'balanced'>('cost');\n  const [requirements, setRequirements] = useState<NutritionalRequirement | null>(null);\n  const [optimizationResult, setOptimizationResult] = useState<OptimizationResult | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [calculatingRequirements, setCalculatingRequirements] = useState(false);\n  const [error, setError] = useState<string>('');\n\n  useEffect(() => {\n    loadFarms();\n  }, []);\n\n  useEffect(() => {\n    if (selectedFarmId) {\n      loadAnimals();\n    }\n  }, [selectedFarmId]);\n\n  useEffect(() => {\n    if (selectedAnimalId) {\n      calculateRequirements();\n    }\n  }, [selectedAnimalId]);\n\n  const loadFarms = async () => {\n    try {\n      const farmsData = await farmApi.getFarms();\n      setFarms(farmsData);\n      if (farmsData.length > 0) {\n        setSelectedFarmId(farmsData[0].id);\n      }\n    } catch (err) {\n      setError('Çiftlikler yüklenirken hata oluştu');\n      console.error(err);\n    }\n  };\n\n  const loadAnimals = async () => {\n    if (!selectedFarmId) return;\n    \n    try {\n      const animalsData = await animalApi.getAnimalsByFarm(selectedFarmId);\n      setAnimals(animalsData);\n      setSelectedAnimalId('');\n      setRequirements(null);\n      setOptimizationResult(null);\n    } catch (err) {\n      setError('Hayvanlar yüklenirken hata oluştu');\n      console.error(err);\n    }\n  };\n\n  const calculateRequirements = async () => {\n    if (!selectedAnimalId) return;\n    \n    setCalculatingRequirements(true);\n    setError('');\n    \n    try {\n      const requirementsData = await rationApi.calculateRequirements(selectedAnimalId);\n      setRequirements(requirementsData);\n      \n      // Otomatik rasyon adı oluştur\n      const selectedAnimal = animals.find(a => a.id === selectedAnimalId);\n      if (selectedAnimal) {\n        setRationName(`${selectedAnimal.breed} - ${selectedAnimal.current_weight_kg}kg - ${new Date().toLocaleDateString('tr-TR')}`);\n      }\n    } catch (err) {\n      setError('Besin ihtiyaçları hesaplanırken hata oluştu');\n      console.error(err);\n    } finally {\n      setCalculatingRequirements(false);\n    }\n  };\n\n  const handleOptimize = async () => {\n    if (!selectedFarmId || !selectedAnimalId || !rationName.trim()) {\n      setError('Lütfen tüm gerekli alanları doldurun');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      const result = await rationApi.optimizeRation({\n        farm_id: selectedFarmId,\n        animal_id: selectedAnimalId,\n        objective,\n        name: rationName.trim(),\n        description: rationDescription.trim() || undefined\n      });\n\n      setOptimizationResult(result);\n    } catch (err: any) {\n      setError(err.response?.data?.detail || 'Rasyon optimizasyonu sırasında hata oluştu');\n      console.error(err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('tr-TR', {\n      style: 'currency',\n      currency: 'TRY'\n    }).format(amount);\n  };\n\n  const getAdequacyColor = (ratio: number) => {\n    if (ratio >= 1.0) return 'text-green-600';\n    if (ratio >= 0.95) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n\n  const getAdequacyText = (ratio: number) => {\n    if (ratio >= 1.0) return 'Yeterli';\n    if (ratio >= 0.95) return 'Sınırda';\n    return 'Yetersiz';\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4\">\n        <button\n          onClick={() => router.back()}\n          className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n        >\n          <ArrowLeft className=\"h-5 w-5 text-gray-600\" />\n        </button>\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Rasyon Optimizasyonu</h1>\n          <p className=\"text-gray-600 mt-2\">\n            Hayvan için optimal rasyon hesaplaması yapın\n          </p>\n        </div>\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n          <div className=\"flex\">\n            <AlertCircle className=\"h-5 w-5 text-red-400\" />\n            <div className=\"ml-3\">\n              <p className=\"text-sm text-red-800\">{error}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Left Column - Input Form */}\n        <div className=\"space-y-6\">\n          {/* Farm and Animal Selection */}\n          <div className=\"content-overlay p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              Hayvan Seçimi\n            </h2>\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Çiftlik\n                </label>\n                <select\n                  value={selectedFarmId}\n                  onChange={(e) => setSelectedFarmId(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                >\n                  <option value=\"\">Çiftlik seçin</option>\n                  {farms.map((farm) => (\n                    <option key={farm.id} value={farm.id}>\n                      {farm.name} - {farm.location}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Hayvan\n                </label>\n                <select\n                  value={selectedAnimalId}\n                  onChange={(e) => setSelectedAnimalId(e.target.value)}\n                  disabled={!selectedFarmId || animals.length === 0}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 disabled:bg-gray-100\"\n                >\n                  <option value=\"\">Hayvan seçin</option>\n                  {animals.map((animal) => (\n                    <option key={animal.id} value={animal.id}>\n                      {animal.breed} - {animal.current_weight_kg}kg - {animal.age_months} ay\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Ration Details */}\n          <div className=\"content-overlay p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              Rasyon Detayları\n            </h2>\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Rasyon Adı *\n                </label>\n                <input\n                  type=\"text\"\n                  value={rationName}\n                  onChange={(e) => setRationName(e.target.value)}\n                  placeholder=\"Rasyon adı girin\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Açıklama\n                </label>\n                <textarea\n                  value={rationDescription}\n                  onChange={(e) => setRationDescription(e.target.value)}\n                  placeholder=\"Rasyon açıklaması (opsiyonel)\"\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Optimizasyon Hedefi\n                </label>\n                <select\n                  value={objective}\n                  onChange={(e) => setObjective(e.target.value as any)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                >\n                  <option value=\"cost\">Maliyet Minimizasyonu</option>\n                  <option value=\"performance\">Performans Maksimizasyonu</option>\n                  <option value=\"balanced\">Dengeli Yaklaşım</option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Optimize Button */}\n          <button\n            onClick={handleOptimize}\n            disabled={!requirements || loading || !rationName.trim()}\n            className=\"w-full btn-primary text-white py-3 rounded-lg flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {loading ? (\n              <>\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\n                <span>Optimize Ediliyor...</span>\n              </>\n            ) : (\n              <>\n                <Calculator className=\"h-5 w-5\" />\n                <span>Rasyonu Optimize Et</span>\n              </>\n            )}\n          </button>\n        </div>\n\n        {/* Right Column - Results */}\n        <div className=\"space-y-6\">\n          {/* Nutritional Requirements */}\n          {calculatingRequirements ? (\n            <div className=\"content-overlay p-6 text-center\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4\"></div>\n              <p className=\"text-gray-600\">Besin ihtiyaçları hesaplanıyor...</p>\n            </div>\n          ) : requirements ? (\n            <div className=\"content-overlay p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                Besin İhtiyaçları\n              </h2>\n              \n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Kuru Madde Alımı:</span>\n                  <span className=\"font-medium\">{requirements.dry_matter_intake_kg.toFixed(1)} kg/gün</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Metabolik Enerji:</span>\n                  <span className=\"font-medium\">{requirements.energy_requirements.metabolizable_energy.toFixed(1)} Mcal/gün</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Ham Protein:</span>\n                  <span className=\"font-medium\">{(requirements.protein_requirements.crude_protein * 1000).toFixed(0)} g/gün</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Kalsiyum:</span>\n                  <span className=\"font-medium\">{(requirements.mineral_requirements.calcium * 1000).toFixed(1)} g/gün</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600\">Fosfor:</span>\n                  <span className=\"font-medium\">{(requirements.mineral_requirements.phosphorus * 1000).toFixed(1)} g/gün</span>\n                </div>\n              </div>\n            </div>\n          ) : selectedAnimalId ? (\n            <div className=\"content-overlay p-6 text-center\">\n              <Target className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-600\">Hayvan seçildi, besin ihtiyaçları hesaplanıyor...</p>\n            </div>\n          ) : (\n            <div className=\"content-overlay p-6 text-center\">\n              <Target className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-600\">Besin ihtiyaçlarını görmek için hayvan seçin</p>\n            </div>\n          )}\n\n          {/* Optimization Results */}\n          {optimizationResult && (\n            <div className=\"content-overlay p-6\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <CheckCircle className=\"h-5 w-5 text-green-500\" />\n                <h2 className=\"text-lg font-semibold text-gray-900\">\n                  Optimizasyon Sonucu\n                </h2>\n              </div>\n              \n              <div className=\"space-y-4\">\n                <div className=\"bg-green-50 p-4 rounded-lg\">\n                  <p className=\"text-green-800 font-medium mb-2\">{optimizationResult.message}</p>\n                  <div className=\"flex items-center space-x-2\">\n                    <DollarSign className=\"h-4 w-4 text-green-600\" />\n                    <span className=\"text-sm text-green-700\">\n                      Günlük Maliyet: {formatCurrency(optimizationResult.optimization_result.total_cost_per_day)}\n                    </span>\n                  </div>\n                </div>\n\n                <div>\n                  <h3 className=\"font-medium text-gray-900 mb-2\">Rasyon Bileşenleri:</h3>\n                  <div className=\"space-y-2\">\n                    {optimizationResult.optimization_result.components.map((component, index) => (\n                      <div key={index} className=\"flex justify-between items-center py-2 border-b border-gray-100\">\n                        <span className=\"text-sm text-gray-600\">{component.feed_name}:</span>\n                        <div className=\"text-right\">\n                          <div className=\"font-medium\">{component.amount_kg.toFixed(1)} kg</div>\n                          <div className=\"text-xs text-gray-500\">{formatCurrency(component.cost)}</div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                <div>\n                  <h3 className=\"font-medium text-gray-900 mb-2\">Besin Yeterliliği:</h3>\n                  <div className=\"space-y-2\">\n                    {Object.entries(optimizationResult.optimization_result.adequacy_ratios).map(([key, ratio]) => (\n                      <div key={key} className=\"flex justify-between items-center\">\n                        <span className=\"text-sm text-gray-600\">\n                          {key === 'protein_adequacy' ? 'Protein' :\n                           key === 'energy_adequacy' ? 'Enerji' :\n                           key === 'calcium_adequacy' ? 'Kalsiyum' : 'Fosfor'}:\n                        </span>\n                        <span className={`font-medium ${getAdequacyColor(ratio as number)}`}>\n                          {((ratio as number) * 100).toFixed(0)}% ({getAdequacyText(ratio as number)})\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                <button\n                  onClick={() => router.push('/rations')}\n                  className=\"w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition-colors\"\n                >\n                  Rasyonları Görüntüle\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;;;AAbA;;;;;AAgBe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC;IAChF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiC;IAChF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IACxF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;QACF;uCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,gBAAgB;gBAClB;YACF;QACF;uCAAG;QAAC;KAAe;IAEnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,kBAAkB;gBACpB;YACF;QACF;uCAAG;QAAC;KAAiB;IAErB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,YAAY,MAAM,yHAAA,CAAA,UAAO,CAAC,QAAQ;YACxC,SAAS;YACT,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,kBAAkB,SAAS,CAAC,EAAE,CAAC,EAAE;YACnC;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,cAAc,MAAM,yHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;YACrD,WAAW;YACX,oBAAoB;YACpB,gBAAgB;YAChB,sBAAsB;QACxB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,kBAAkB;QAEvB,2BAA2B;QAC3B,SAAS;QAET,IAAI;YACF,MAAM,mBAAmB,MAAM,yHAAA,CAAA,YAAS,CAAC,qBAAqB,CAAC;YAC/D,gBAAgB;YAEhB,8BAA8B;YAC9B,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAClD,IAAI,gBAAgB;gBAClB,cAAc,GAAG,eAAe,KAAK,CAAC,GAAG,EAAE,eAAe,iBAAiB,CAAC,KAAK,EAAE,IAAI,OAAO,kBAAkB,CAAC,UAAU;YAC7H;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,2BAA2B;QAC7B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,WAAW,IAAI,IAAI;YAC9D,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc,CAAC;gBAC5C,SAAS;gBACT,WAAW;gBACX;gBACA,MAAM,WAAW,IAAI;gBACrB,aAAa,kBAAkB,IAAI,MAAM;YAC3C;YAEA,sBAAsB;QACxB,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,UAAU;YACvC,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,MAAM,OAAO;QAC1B,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,MAAM,OAAO;QAC1B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI;wBAC1B,WAAU;kCAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;YAOrC,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAM7C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAIzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;;0EAEV,6LAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;oEAAqB,OAAO,KAAK,EAAE;;wEACjC,KAAK,IAAI;wEAAC;wEAAI,KAAK,QAAQ;;mEADjB,KAAK,EAAE;;;;;;;;;;;;;;;;;0DAO1B,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wDACnD,UAAU,CAAC,kBAAkB,QAAQ,MAAM,KAAK;wDAChD,WAAU;;0EAEV,6LAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oEAAuB,OAAO,OAAO,EAAE;;wEACrC,OAAO,KAAK;wEAAC;wEAAI,OAAO,iBAAiB;wEAAC;wEAAM,OAAO,UAAU;wEAAC;;mEADxD,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUhC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAIzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAId,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;wDACpD,aAAY;wDACZ,MAAM;wDACN,WAAU;;;;;;;;;;;;0DAId,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wDAC5C,WAAU;;0EAEV,6LAAC;gEAAO,OAAM;0EAAO;;;;;;0EACrB,6LAAC;gEAAO,OAAM;0EAAc;;;;;;0EAC5B,6LAAC;gEAAO,OAAM;0EAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOjC,6LAAC;gCACC,SAAS;gCACT,UAAU,CAAC,gBAAgB,WAAW,CAAC,WAAW,IAAI;gCACtD,WAAU;0CAET,wBACC;;sDACE,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;sDAAK;;;;;;;iEAGR;;sDACE,6LAAC,iNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;kCAOd,6LAAC;wBAAI,WAAU;;4BAEZ,wCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;uCAE7B,6BACF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAIzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC;wDAAK,WAAU;;4DAAe,aAAa,oBAAoB,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAE9E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC;wDAAK,WAAU;;4DAAe,aAAa,mBAAmB,CAAC,oBAAoB,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAElG,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC;wDAAK,WAAU;;4DAAe,CAAC,aAAa,oBAAoB,CAAC,aAAa,GAAG,IAAI,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAErG,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC;wDAAK,WAAU;;4DAAe,CAAC,aAAa,oBAAoB,CAAC,OAAO,GAAG,IAAI,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAE/F,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC;wDAAK,WAAU;;4DAAe,CAAC,aAAa,oBAAoB,CAAC,UAAU,GAAG,IAAI,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;;;;;;uCAIpG,iCACF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;qDAG/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;4BAKhC,oCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;;kDAKtD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAmC,mBAAmB,OAAO;;;;;;kEAC1E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;gEAAK,WAAU;;oEAAyB;oEACtB,eAAe,mBAAmB,mBAAmB,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;0DAK/F,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;kEACZ,mBAAmB,mBAAmB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACjE,6LAAC;gEAAgB,WAAU;;kFACzB,6LAAC;wEAAK,WAAU;;4EAAyB,UAAU,SAAS;4EAAC;;;;;;;kFAC7D,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;oFAAe,UAAU,SAAS,CAAC,OAAO,CAAC;oFAAG;;;;;;;0FAC7D,6LAAC;gFAAI,WAAU;0FAAyB,eAAe,UAAU,IAAI;;;;;;;;;;;;;+DAJ/D;;;;;;;;;;;;;;;;0DAWhB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;kEACZ,OAAO,OAAO,CAAC,mBAAmB,mBAAmB,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACvF,6LAAC;gEAAc,WAAU;;kFACvB,6LAAC;wEAAK,WAAU;;4EACb,QAAQ,qBAAqB,YAC7B,QAAQ,oBAAoB,WAC5B,QAAQ,qBAAqB,aAAa;4EAAS;;;;;;;kFAEtD,6LAAC;wEAAK,WAAW,CAAC,YAAY,EAAE,iBAAiB,QAAkB;;4EAChE,CAAC,AAAC,QAAmB,GAAG,EAAE,OAAO,CAAC;4EAAG;4EAAI,gBAAgB;4EAAiB;;;;;;;;+DAPrE;;;;;;;;;;;;;;;;0DAchB,6LAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAzYwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}