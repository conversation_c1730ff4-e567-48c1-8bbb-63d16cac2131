from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import uuid
import sqlite3
import json
from typing import List, Dict, Any

# SQLite veritabanı kurulumu
def init_database():
    conn = sqlite3.connect('hayvancilik.db')
    cursor = conn.cursor()

    # Çiftlik tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS farms (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            location TEXT NOT NULL,
            established_date TEXT NOT NULL,
            total_land_hectares REAL,
            pasture_land_hectares REAL,
            barn_capacity INTEGER,
            feed_storage_capacity_tons REAL,
            silage_capacity_tons REAL,
            hay_storage_capacity_tons REAL,
            water_storage_capacity_liters REAL,
            milking_parlor_capacity INTEGER,
            quarantine_facility_capacity INTEGER,
            hospital_pen_capacity INTEGER,
            handling_facility_present BOOLEAN,
            scale_capacity_kg REAL,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Hayvan tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS animals (
            id TEXT PRIMARY KEY,
            farm_id TEXT NOT NULL,
            breed TEXT NOT NULL,
            birth_date TEXT NOT NULL,
            gender TEXT NOT NULL,
            current_weight_kg REAL NOT NULL,
            body_condition_score REAL NOT NULL,
            status TEXT NOT NULL,
            is_pregnant BOOLEAN DEFAULT FALSE,
            pregnancy_start_date TEXT,
            expected_calving_date TEXT,
            dam_id TEXT,
            sire_id TEXT,
            purchase_price REAL,
            purchase_date TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (farm_id) REFERENCES farms (id)
        )
    ''')

    # Yem tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS feeds (
            id TEXT PRIMARY KEY,
            farm_id TEXT NOT NULL,
            name TEXT NOT NULL,
            feed_type TEXT NOT NULL,
            cost_per_kg REAL NOT NULL,
            dry_matter_percentage REAL NOT NULL,
            crude_protein_percentage REAL NOT NULL,
            metabolizable_energy_mcal_kg REAL NOT NULL,
            storage_life_days INTEGER,
            moisture_content_percentage REAL,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (farm_id) REFERENCES farms (id)
        )
    ''')

    # Rasyon tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS rations (
            id TEXT PRIMARY KEY,
            farm_id TEXT NOT NULL,
            animal_id TEXT,
            name TEXT NOT NULL,
            description TEXT,
            ration_type TEXT NOT NULL DEFAULT 'individual',
            target_group TEXT,
            total_cost_per_day REAL NOT NULL,
            total_dry_matter_kg REAL NOT NULL,
            total_crude_protein_percentage REAL NOT NULL,
            total_metabolizable_energy_mcal REAL NOT NULL,
            total_calcium_percentage REAL NOT NULL,
            total_phosphorus_percentage REAL NOT NULL,
            is_optimized BOOLEAN DEFAULT FALSE,
            optimization_objective TEXT,
            optimization_score REAL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (farm_id) REFERENCES farms (id),
            FOREIGN KEY (animal_id) REFERENCES animals (id)
        )
    ''')

    # Rasyon bileşenleri tablosu
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ration_components (
            id TEXT PRIMARY KEY,
            ration_id TEXT NOT NULL,
            feed_id TEXT NOT NULL,
            amount_kg_per_day REAL NOT NULL,
            percentage_of_total_dm REAL NOT NULL,
            dry_matter_contribution_kg REAL NOT NULL,
            protein_contribution_kg REAL NOT NULL,
            energy_contribution_mcal REAL NOT NULL,
            calcium_contribution_kg REAL NOT NULL,
            phosphorus_contribution_kg REAL NOT NULL,
            cost_contribution REAL NOT NULL,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (ration_id) REFERENCES rations (id),
            FOREIGN KEY (feed_id) REFERENCES feeds (id)
        )
    ''')

    conn.commit()
    conn.close()
    print("✓ Veritabanı başarıyla oluşturuldu")

def get_db_connection():
    conn = sqlite3.connect('hayvancilik.db')
    conn.row_factory = sqlite3.Row  # Dict-like access
    return conn

# Veritabanını başlat
init_database()

# FastAPI uygulaması
app = FastAPI(
    title="Hayvan Yetiştiriciliği Simülasyon Sistemi",
    description="Sığır yetiştiriciliği için kapsamlı simülasyon ve yönetim sistemi",
    version="1.0.0"
)

# CORS middleware ekle
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React frontend
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "Hayvan Yetiştiriciliği Simülasyon Sistemi API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

# Çiftlik API endpoint'leri
@app.get("/api/farms/")
async def get_farms():
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM farms ORDER BY created_at DESC")
    farms = cursor.fetchall()
    conn.close()

    return [dict(farm) for farm in farms]

@app.post("/api/farms/")
async def create_farm(farm_data: dict):
    try:
        farm_id = str(uuid.uuid4())
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO farms (
                id, name, location, established_date,
                total_land_hectares, pasture_land_hectares, barn_capacity,
                feed_storage_capacity_tons, silage_capacity_tons, hay_storage_capacity_tons,
                water_storage_capacity_liters, milking_parlor_capacity,
                quarantine_facility_capacity, hospital_pen_capacity,
                handling_facility_present, scale_capacity_kg
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            farm_id,
            farm_data.get('name'),
            farm_data.get('location'),
            datetime.now().isoformat(),
            farm_data.get('total_land_hectares', 0),
            farm_data.get('pasture_land_hectares', 0),
            farm_data.get('barn_capacity', 0),
            farm_data.get('feed_storage_capacity_tons', 0),
            farm_data.get('silage_capacity_tons', 0),
            farm_data.get('hay_storage_capacity_tons', 0),
            farm_data.get('water_storage_capacity_liters', 0),
            farm_data.get('milking_parlor_capacity'),
            farm_data.get('quarantine_facility_capacity', 0),
            farm_data.get('hospital_pen_capacity', 0),
            farm_data.get('handling_facility_present', False),
            farm_data.get('scale_capacity_kg', 0)
        ))

        conn.commit()
        conn.close()

        print(f"✓ Çiftlik oluşturuldu: {farm_data.get('name')} (ID: {farm_id})")
        return {"id": farm_id, "message": "Çiftlik başarıyla oluşturuldu"}

    except Exception as e:
        print(f"❌ Çiftlik oluşturma hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Çiftlik oluşturulamadı: {str(e)}")

# Hayvan API endpoint'leri
@app.get("/api/animals/farm/{farm_id}")
async def get_animals(farm_id: str):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM animals WHERE farm_id = ? ORDER BY created_at DESC", (farm_id,))
    animals = cursor.fetchall()
    conn.close()

    # Yaş hesaplama
    result = []
    for animal in animals:
        animal_dict = dict(animal)
        birth_date = datetime.fromisoformat(animal_dict['birth_date'])
        age_months = int((datetime.now() - birth_date).days / 30.44)
        animal_dict['age_months'] = age_months
        result.append(animal_dict)

    return result

@app.get("/api/animals/farm/{farm_id}/stats")
async def get_animal_stats(farm_id: str):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM animals WHERE farm_id = ?", (farm_id,))
    animals = cursor.fetchall()
    conn.close()

    if not animals:
        return {
            "total_animals": 0,
            "by_gender": {"male": 0, "female": 0},
            "by_status": {},
            "by_breed": {},
            "average_weight": 0,
            "pregnant_count": 0
        }

    stats = {
        "total_animals": len(animals),
        "by_gender": {"male": 0, "female": 0},
        "by_status": {},
        "by_breed": {},
        "average_weight": sum(animal['current_weight_kg'] for animal in animals) / len(animals),
        "pregnant_count": sum(1 for animal in animals if animal['is_pregnant'])
    }

    for animal in animals:
        # Cinsiyet istatistikleri
        stats["by_gender"][animal['gender']] += 1

        # Durum istatistikleri
        if animal['status'] not in stats["by_status"]:
            stats["by_status"][animal['status']] = 0
        stats["by_status"][animal['status']] += 1

        # Irk istatistikleri
        if animal['breed'] not in stats["by_breed"]:
            stats["by_breed"][animal['breed']] = 0
        stats["by_breed"][animal['breed']] += 1

    return stats

@app.post("/api/animals/")
async def create_animal(animal_data: dict):
    try:
        animal_id = str(uuid.uuid4())
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO animals (
                id, farm_id, breed, birth_date, gender,
                current_weight_kg, body_condition_score, status,
                is_pregnant, pregnancy_start_date, expected_calving_date,
                dam_id, sire_id, purchase_price, purchase_date
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            animal_id,
            animal_data.get('farm_id'),
            animal_data.get('breed'),
            animal_data.get('birth_date'),
            animal_data.get('gender'),
            animal_data.get('current_weight_kg'),
            animal_data.get('body_condition_score'),
            animal_data.get('status'),
            animal_data.get('is_pregnant', False),
            animal_data.get('pregnancy_start_date'),
            animal_data.get('expected_calving_date'),
            animal_data.get('dam_id'),
            animal_data.get('sire_id'),
            animal_data.get('purchase_price'),
            animal_data.get('purchase_date')
        ))

        conn.commit()
        conn.close()

        print(f"✓ Hayvan eklendi: {animal_data.get('breed')} (ID: {animal_id})")
        return {"id": animal_id, "message": "Hayvan başarıyla eklendi"}

    except Exception as e:
        print(f"❌ Hayvan ekleme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Hayvan eklenemedi: {str(e)}")

@app.get("/api/animals/{animal_id}")
async def get_animal(animal_id: str):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM animals WHERE id = ?", (animal_id,))
    animal = cursor.fetchone()
    conn.close()

    if not animal:
        raise HTTPException(status_code=404, detail="Hayvan bulunamadı")

    animal_dict = dict(animal)
    birth_date = datetime.fromisoformat(animal_dict['birth_date'])
    age_months = int((datetime.now() - birth_date).days / 30.44)
    animal_dict['age_months'] = age_months

    return animal_dict

@app.put("/api/animals/{animal_id}")
async def update_animal(animal_id: str, animal_data: dict):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Önce hayvanın var olduğunu kontrol et
        cursor.execute("SELECT id FROM animals WHERE id = ?", (animal_id,))
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail="Hayvan bulunamadı")

        cursor.execute('''
            UPDATE animals SET
                breed = ?, birth_date = ?, gender = ?,
                current_weight_kg = ?, body_condition_score = ?, status = ?,
                is_pregnant = ?, pregnancy_start_date = ?, expected_calving_date = ?,
                dam_id = ?, sire_id = ?, purchase_price = ?, purchase_date = ?
            WHERE id = ?
        ''', (
            animal_data.get('breed'),
            animal_data.get('birth_date'),
            animal_data.get('gender'),
            animal_data.get('current_weight_kg'),
            animal_data.get('body_condition_score'),
            animal_data.get('status'),
            animal_data.get('is_pregnant', False),
            animal_data.get('pregnancy_start_date'),
            animal_data.get('expected_calving_date'),
            animal_data.get('dam_id'),
            animal_data.get('sire_id'),
            animal_data.get('purchase_price'),
            animal_data.get('purchase_date'),
            animal_id
        ))

        conn.commit()
        conn.close()

        print(f"✓ Hayvan güncellendi: {animal_data.get('breed')} (ID: {animal_id})")
        return {"message": "Hayvan başarıyla güncellendi"}

    except Exception as e:
        print(f"❌ Hayvan güncelleme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Hayvan güncellenemedi: {str(e)}")

@app.delete("/api/animals/{animal_id}")
async def delete_animal(animal_id: str):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Önce hayvanın var olduğunu kontrol et
        cursor.execute("SELECT id FROM animals WHERE id = ?", (animal_id,))
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail="Hayvan bulunamadı")

        cursor.execute("DELETE FROM animals WHERE id = ?", (animal_id,))
        conn.commit()
        conn.close()

        print(f"✓ Hayvan silindi: {animal_id}")
        return {"message": "Hayvan başarıyla silindi"}

    except Exception as e:
        print(f"❌ Hayvan silme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Hayvan silinemedi: {str(e)}")

# Yem API endpoint'leri
@app.get("/api/feeds/farm/{farm_id}")
async def get_feeds(farm_id: str):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM feeds WHERE farm_id = ? ORDER BY created_at DESC", (farm_id,))
    feeds = cursor.fetchall()
    conn.close()

    return [dict(feed) for feed in feeds]

@app.post("/api/feeds/")
async def create_feed(feed_data: dict):
    try:
        feed_id = str(uuid.uuid4())
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO feeds (
                id, farm_id, name, feed_type, cost_per_kg,
                dry_matter_percentage, crude_protein_percentage,
                metabolizable_energy_mcal_kg, storage_life_days,
                moisture_content_percentage
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            feed_id,
            feed_data.get('farm_id'),
            feed_data.get('name'),
            feed_data.get('feed_type'),
            feed_data.get('cost_per_kg'),
            feed_data.get('dry_matter_percentage'),
            feed_data.get('crude_protein_percentage'),
            feed_data.get('metabolizable_energy_mcal_kg'),
            feed_data.get('storage_life_days'),
            feed_data.get('moisture_content_percentage')
        ))

        conn.commit()
        conn.close()

        print(f"✓ Yem eklendi: {feed_data.get('name')} (ID: {feed_id})")
        return {"id": feed_id, "message": "Yem başarıyla eklendi"}

    except Exception as e:
        print(f"❌ Yem ekleme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Yem eklenemedi: {str(e)}")

@app.post("/api/feeds/farm/{farm_id}/sample-feeds")
async def add_sample_feeds(farm_id: str):
    try:
        sample_feeds = [
            {
                "name": "Konsantre Yem",
                "feed_type": "concentrate",
                "cost_per_kg": 3.5,
                "dry_matter_percentage": 88.0,
                "crude_protein_percentage": 18.0,
                "metabolizable_energy_mcal_kg": 2.8,
                "storage_life_days": 180,
                "moisture_content_percentage": 12.0
            },
            {
                "name": "Kuru Ot",
                "feed_type": "hay",
                "cost_per_kg": 0.8,
                "dry_matter_percentage": 85.0,
                "crude_protein_percentage": 8.0,
                "metabolizable_energy_mcal_kg": 2.0,
                "storage_life_days": 365,
                "moisture_content_percentage": 15.0
            },
            {
                "name": "Mısır Silajı",
                "feed_type": "silage",
                "cost_per_kg": 0.3,
                "dry_matter_percentage": 35.0,
                "crude_protein_percentage": 7.5,
                "metabolizable_energy_mcal_kg": 2.4,
                "storage_life_days": 365,
                "moisture_content_percentage": 65.0
            }
        ]

        conn = get_db_connection()
        cursor = conn.cursor()
        added_feeds = []

        for feed_data in sample_feeds:
            feed_id = str(uuid.uuid4())
            feed_data["farm_id"] = farm_id

            cursor.execute('''
                INSERT INTO feeds (
                    id, farm_id, name, feed_type, cost_per_kg,
                    dry_matter_percentage, crude_protein_percentage,
                    metabolizable_energy_mcal_kg, storage_life_days,
                    moisture_content_percentage
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                feed_id,
                feed_data["farm_id"],
                feed_data["name"],
                feed_data["feed_type"],
                feed_data["cost_per_kg"],
                feed_data["dry_matter_percentage"],
                feed_data["crude_protein_percentage"],
                feed_data["metabolizable_energy_mcal_kg"],
                feed_data["storage_life_days"],
                feed_data["moisture_content_percentage"]
            ))
            added_feeds.append(feed_data["name"])

        conn.commit()
        conn.close()

        print(f"✓ {len(added_feeds)} örnek yem eklendi")
        return {
            "message": f"{len(added_feeds)} örnek yem başarıyla eklendi",
            "feeds": added_feeds
        }

    except Exception as e:
        print(f"❌ Örnek yem ekleme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Örnek yemler eklenemedi: {str(e)}")

@app.get("/api/feeds/{feed_id}")
async def get_feed(feed_id: str):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM feeds WHERE id = ?", (feed_id,))
    feed = cursor.fetchone()
    conn.close()

    if not feed:
        raise HTTPException(status_code=404, detail="Yem bulunamadı")

    return dict(feed)

@app.put("/api/feeds/{feed_id}")
async def update_feed(feed_id: str, feed_data: dict):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Önce yemin var olduğunu kontrol et
        cursor.execute("SELECT id FROM feeds WHERE id = ?", (feed_id,))
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail="Yem bulunamadı")

        cursor.execute('''
            UPDATE feeds SET
                name = ?, feed_type = ?, cost_per_kg = ?,
                dry_matter_percentage = ?, crude_protein_percentage = ?,
                metabolizable_energy_mcal_kg = ?, storage_life_days = ?,
                moisture_content_percentage = ?
            WHERE id = ?
        ''', (
            feed_data.get('name'),
            feed_data.get('feed_type'),
            feed_data.get('cost_per_kg'),
            feed_data.get('dry_matter_percentage'),
            feed_data.get('crude_protein_percentage'),
            feed_data.get('metabolizable_energy_mcal_kg'),
            feed_data.get('storage_life_days'),
            feed_data.get('moisture_content_percentage'),
            feed_id
        ))

        conn.commit()
        conn.close()

        print(f"✓ Yem güncellendi: {feed_data.get('name')} (ID: {feed_id})")
        return {"message": "Yem başarıyla güncellendi"}

    except Exception as e:
        print(f"❌ Yem güncelleme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Yem güncellenemedi: {str(e)}")

@app.delete("/api/feeds/{feed_id}")
async def delete_feed(feed_id: str):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Önce yemin var olduğunu kontrol et
        cursor.execute("SELECT id FROM feeds WHERE id = ?", (feed_id,))
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail="Yem bulunamadı")

        cursor.execute("DELETE FROM feeds WHERE id = ?", (feed_id,))
        conn.commit()
        conn.close()

        print(f"✓ Yem silindi: {feed_id}")
        return {"message": "Yem başarıyla silindi"}

    except Exception as e:
        print(f"❌ Yem silme hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Yem silinemedi: {str(e)}")

# Rasyon API endpoint'leri
@app.get("/api/rations/farm/{farm_id}")
async def get_rations(farm_id: str):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM rations WHERE farm_id = ? AND is_active = 1 ORDER BY created_at DESC", (farm_id,))
    rations = cursor.fetchall()
    conn.close()

    return [dict(ration) for ration in rations]

@app.post("/api/rations/calculate-requirements")
async def calculate_requirements(animal_id: str):
    """Basit besin ihtiyacı hesaplama"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM animals WHERE id = ?", (animal_id,))
        animal = cursor.fetchone()
        conn.close()

        if not animal:
            raise HTTPException(status_code=404, detail="Hayvan bulunamadı")

        animal_dict = dict(animal)
        weight = animal_dict['current_weight_kg']

        # Basit NRC hesaplamaları
        dmi = weight * 0.025  # %2.5 vücut ağırlığı
        energy = weight * 0.077 * (weight ** -0.25) / 0.64  # Basit ME hesabı
        protein = weight * 0.004  # Basit protein hesabı
        calcium = weight * 0.00004  # 40mg/kg
        phosphorus = weight * 0.00003  # 30mg/kg

        return {
            "animal_id": animal_id,
            "dry_matter_intake_kg": round(dmi, 2),
            "energy_requirements": {
                "metabolizable_energy": round(energy, 2)
            },
            "protein_requirements": {
                "crude_protein": round(protein, 3)
            },
            "mineral_requirements": {
                "calcium": round(calcium, 4),
                "phosphorus": round(phosphorus, 4)
            }
        }

    except Exception as e:
        print(f"❌ Besin ihtiyacı hesaplama hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Hesaplama hatası: {str(e)}")

@app.post("/api/rations/optimize")
async def optimize_ration(request_data: dict):
    """Basit rasyon optimizasyonu"""
    try:
        farm_id = request_data.get('farm_id')
        animal_id = request_data.get('animal_id')
        name = request_data.get('name')

        # Hayvan bilgilerini al
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM animals WHERE id = ?", (animal_id,))
        animal = cursor.fetchone()

        if not animal:
            raise HTTPException(status_code=404, detail="Hayvan bulunamadı")

        # Çiftlikteki yemleri al
        cursor.execute("SELECT * FROM feeds WHERE farm_id = ?", (farm_id,))
        feeds = cursor.fetchall()

        if len(feeds) < 2:
            raise HTTPException(status_code=400, detail="En az 2 yem gerekli")

        # Basit optimizasyon: En ucuz konsantre + en ucuz kaba yem
        concentrates = [f for f in feeds if dict(f)['feed_type'] == 'concentrate']
        forages = [f for f in feeds if dict(f)['feed_type'] in ['hay', 'silage']]

        if not concentrates or not forages:
            raise HTTPException(status_code=400, detail="Hem konsantre hem kaba yem gerekli")

        best_concentrate = min(concentrates, key=lambda f: dict(f)['cost_per_kg'])
        best_forage = min(forages, key=lambda f: dict(f)['cost_per_kg'])

        # Basit rasyon: %30 konsantre, %70 kaba yem
        animal_dict = dict(animal)
        target_dmi = animal_dict['current_weight_kg'] * 0.025

        conc_amount = target_dmi * 0.3
        forage_amount = target_dmi * 0.7

        conc_dict = dict(best_concentrate)
        forage_dict = dict(best_forage)

        total_cost = (conc_amount * conc_dict['cost_per_kg']) + (forage_amount * forage_dict['cost_per_kg'])

        # Rasyon kaydını oluştur
        ration_id = str(uuid.uuid4())
        cursor.execute('''
            INSERT INTO rations (
                id, farm_id, animal_id, name, ration_type,
                total_cost_per_day, total_dry_matter_kg,
                total_crude_protein_percentage, total_metabolizable_energy_mcal,
                total_calcium_percentage, total_phosphorus_percentage,
                is_optimized, optimization_objective
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            ration_id, farm_id, animal_id, name, 'individual',
            total_cost, target_dmi, 15.0, 2.5, 0.6, 0.4, True, 'cost'
        ))

        # Bileşenleri ekle
        comp1_id = str(uuid.uuid4())
        cursor.execute('''
            INSERT INTO ration_components (
                id, ration_id, feed_id, amount_kg_per_day, percentage_of_total_dm,
                dry_matter_contribution_kg, protein_contribution_kg, energy_contribution_mcal,
                calcium_contribution_kg, phosphorus_contribution_kg, cost_contribution
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            comp1_id, ration_id, conc_dict['id'], conc_amount, 30.0,
            conc_amount * 0.88, conc_amount * 0.18, conc_amount * 2.8,
            conc_amount * 0.008, conc_amount * 0.004, conc_amount * conc_dict['cost_per_kg']
        ))

        comp2_id = str(uuid.uuid4())
        cursor.execute('''
            INSERT INTO ration_components (
                id, ration_id, feed_id, amount_kg_per_day, percentage_of_total_dm,
                dry_matter_contribution_kg, protein_contribution_kg, energy_contribution_mcal,
                calcium_contribution_kg, phosphorus_contribution_kg, cost_contribution
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            comp2_id, ration_id, forage_dict['id'], forage_amount, 70.0,
            forage_amount * 0.85, forage_amount * 0.08, forage_amount * 2.0,
            forage_amount * 0.004, forage_amount * 0.002, forage_amount * forage_dict['cost_per_kg']
        ))

        conn.commit()
        conn.close()

        return {
            "ration_id": ration_id,
            "message": "Rasyon başarıyla optimize edildi",
            "optimization_result": {
                "total_cost_per_day": round(total_cost, 2),
                "total_dry_matter_kg": round(target_dmi, 2),
                "components": [
                    {
                        "feed_name": conc_dict['name'],
                        "amount_kg": round(conc_amount, 2),
                        "cost": round(conc_amount * conc_dict['cost_per_kg'], 2)
                    },
                    {
                        "feed_name": forage_dict['name'],
                        "amount_kg": round(forage_amount, 2),
                        "cost": round(forage_amount * forage_dict['cost_per_kg'], 2)
                    }
                ],
                "adequacy_ratios": {
                    "protein_adequacy": 1.0,
                    "energy_adequacy": 1.0,
                    "calcium_adequacy": 1.0,
                    "phosphorus_adequacy": 1.0
                }
            }
        }

    except Exception as e:
        print(f"❌ Rasyon optimizasyon hatası: {e}")
        raise HTTPException(status_code=500, detail=f"Optimizasyon hatası: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
