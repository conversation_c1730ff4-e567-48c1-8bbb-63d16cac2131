"""
Rasyon hesaplama servisleri
NRC standartlarına göre besin ihtiyacı hesaplama ve rasyon optimizasyonu
"""

import math
from typing import Dict, List, Optional, Tuple
from sqlalchemy.orm import Session
from ..database import models
from ..models.schemas import CattleBreed, Gender


class NRCCalculator:
    """NRC (National Research Council) standartlarına göre besin ihtiyacı hesaplama"""

    @staticmethod
    def calculate_body_weight_from_age(breed: str, gender: str, age_months: int) -> float:
        """Yaş ve ırka göre tahmini vücut ağırlığı hesaplama"""
        # Temel ağırlık değerleri (kg)
        breed_weights = {
            "angus": {"male": 850, "female": 550},
            "holstein": {"male": 900, "female": 650},
            "simmental": {"male": 1100, "female": 750},
            "charolais": {"male": 1200, "female": 800},
            "limousin": {"male": 1000, "female": 650},
            "hereford": {"male": 900, "female": 600}
        }

        breed_key = breed.lower()
        if breed_key not in breed_weights:
            breed_key = "angus"  # Default

        mature_weight = breed_weights[breed_key][gender.lower()]

        # Büyüme eğrisi (Gompertz modeli)
        if age_months <= 24:
            # Genç hayvanlar için hızlı büyüme
            growth_rate = 1 - math.exp(-0.15 * age_months)
        else:
            # Olgun hayvanlar için yavaş büyüme
            growth_rate = 0.95 + 0.05 * (1 - math.exp(-0.05 * (age_months - 24)))

        return mature_weight * growth_rate

    @staticmethod
    def calculate_dry_matter_intake(weight_kg: float, body_condition_score: float,
                                  is_pregnant: bool = False, is_lactating: bool = False,
                                  activity_level: str = "moderate") -> float:
        """Kuru madde alımı hesaplama (kg/gün)"""
        # Temel DMI = Vücut ağırlığının %2-3'ü
        base_dmi = weight_kg * 0.025

        # Vücut kondisyon skoru düzeltmesi
        if body_condition_score < 2.5:
            base_dmi *= 1.1  # Zayıf hayvanlar daha fazla yer
        elif body_condition_score > 4.0:
            base_dmi *= 0.9  # Şişman hayvanlar daha az yer

        # Fizyolojik durum düzeltmeleri
        if is_pregnant:
            base_dmi *= 1.15  # Gebe hayvanlar %15 daha fazla
        if is_lactating:
            base_dmi *= 1.25  # Süt veren hayvanlar %25 daha fazla

        # Aktivite seviyesi düzeltmesi
        activity_multipliers = {
            "low": 0.95,
            "moderate": 1.0,
            "high": 1.1
        }
        base_dmi *= activity_multipliers.get(activity_level, 1.0)

        return round(base_dmi, 2)

    @staticmethod
    def calculate_energy_requirements(weight_kg: float, average_daily_gain: float,
                                    is_pregnant: bool = False, is_lactating: bool = False,
                                    temperature_celsius: float = 20.0) -> Dict[str, float]:
        """Enerji ihtiyaçları hesaplama (Mcal/gün)"""

        # Net Energy for Maintenance (NEm)
        # NEm = 0.077 * BW^0.75
        nem = 0.077 * (weight_kg ** 0.75)

        # Sıcaklık düzeltmesi
        if temperature_celsius < 5:
            nem *= 1.2  # Soğukta %20 daha fazla
        elif temperature_celsius > 30:
            nem *= 1.1  # Sıcakta %10 daha fazla

        # Net Energy for Gain (NEg)
        if average_daily_gain > 0:
            # NEg = 0.0557 * BW^0.75 * ADG^1.097
            neg = 0.0557 * (weight_kg ** 0.75) * (average_daily_gain ** 1.097)
        else:
            neg = 0.0

        # Gebelik enerji ihtiyacı
        pregnancy_energy = 0.0
        if is_pregnant:
            pregnancy_energy = 2.0  # Son trimesterde 2 Mcal/gün

        # Laktasyon enerji ihtiyacı
        lactation_energy = 0.0
        if is_lactating:
            lactation_energy = 8.0  # Ortalama 8 Mcal/gün (süt verimine bağlı)

        # Metabolizable Energy (ME) = (NEm + NEg + NEp + NEl) / efficiency
        total_ne = nem + neg + pregnancy_energy + lactation_energy
        me = total_ne / 0.64  # %64 verimlilik

        return {
            "net_energy_maintenance": round(nem, 2),
            "net_energy_gain": round(neg, 2),
            "pregnancy_energy": round(pregnancy_energy, 2),
            "lactation_energy": round(lactation_energy, 2),
            "total_net_energy": round(total_ne, 2),
            "metabolizable_energy": round(me, 2)
        }

    @staticmethod
    def calculate_protein_requirements(weight_kg: float, average_daily_gain: float,
                                     is_pregnant: bool = False, is_lactating: bool = False) -> Dict[str, float]:
        """Protein ihtiyaçları hesaplama (kg/gün)"""

        # Metabolizable Protein for Maintenance (MPm)
        # MPm = 3.8 * BW^0.75 / 1000
        mpm = 3.8 * (weight_kg ** 0.75) / 1000

        # Metabolizable Protein for Gain (MPg)
        if average_daily_gain > 0:
            # MPg = (ADG * 268 - 29.4) / 1000
            mpg = max(0, (average_daily_gain * 268 - 29.4) / 1000)
        else:
            mpg = 0.0

        # Gebelik protein ihtiyacı
        pregnancy_protein = 0.0
        if is_pregnant:
            pregnancy_protein = 0.1  # Son trimesterde 100g/gün

        # Laktasyon protein ihtiyacı
        lactation_protein = 0.0
        if is_lactating:
            lactation_protein = 0.5  # Ortalama 500g/gün

        total_mp = mpm + mpg + pregnancy_protein + lactation_protein

        # Crude Protein = MP / 0.64 (protein efficiency)
        crude_protein = total_mp / 0.64

        # RDP ve RUP hesaplama
        rdp = crude_protein * 0.65  # %65 RDP
        rup = crude_protein * 0.35  # %35 RUP

        return {
            "metabolizable_protein_maintenance": round(mpm, 3),
            "metabolizable_protein_gain": round(mpg, 3),
            "pregnancy_protein": round(pregnancy_protein, 3),
            "lactation_protein": round(lactation_protein, 3),
            "total_metabolizable_protein": round(total_mp, 3),
            "crude_protein": round(crude_protein, 3),
            "rumen_degradable_protein": round(rdp, 3),
            "rumen_undegradable_protein": round(rup, 3)
        }

    @staticmethod
    def calculate_mineral_requirements(weight_kg: float, is_pregnant: bool = False,
                                     is_lactating: bool = False) -> Dict[str, float]:
        """Mineral ihtiyaçları hesaplama (kg/gün)"""

        # Kalsiyum ihtiyacı
        calcium_base = weight_kg * 0.00004  # 40mg/kg vücut ağırlığı
        if is_pregnant:
            calcium_base *= 1.5
        if is_lactating:
            calcium_base *= 2.0

        # Fosfor ihtiyacı
        phosphorus_base = weight_kg * 0.00003  # 30mg/kg vücut ağırlığı
        if is_pregnant:
            phosphorus_base *= 1.3
        if is_lactating:
            phosphorus_base *= 1.8

        return {
            "calcium": round(calcium_base, 4),
            "phosphorus": round(phosphorus_base, 4)
        }


class RationOptimizer:
    """Rasyon optimizasyon servisi"""

    def __init__(self, db: Session):
        self.db = db
        self.nrc = NRCCalculator()

    def calculate_nutritional_requirements(self, animal_id: str) -> Dict:
        """Hayvan için besin ihtiyaçlarını hesapla"""
        animal = self.db.query(models.Animal).filter(models.Animal.id == animal_id).first()
        if not animal:
            raise ValueError("Hayvan bulunamadı")

        # Yaş hesaplama
        from datetime import datetime
        age_months = int((datetime.now() - animal.birth_date).days / 30.44)

        # Tahmini günlük artış (breed characteristics'ten al)
        breed_char = self.db.query(models.BreedCharacteristic).filter(
            models.BreedCharacteristic.breed == animal.breed
        ).first()

        avg_daily_gain = breed_char.average_daily_gain_kg if breed_char else 1.0

        # Besin ihtiyaçlarını hesapla
        dmi = self.nrc.calculate_dry_matter_intake(
            animal.current_weight_kg,
            animal.body_condition_score,
            animal.is_pregnant
        )

        energy_req = self.nrc.calculate_energy_requirements(
            animal.current_weight_kg,
            avg_daily_gain,
            animal.is_pregnant
        )

        protein_req = self.nrc.calculate_protein_requirements(
            animal.current_weight_kg,
            avg_daily_gain,
            animal.is_pregnant
        )

        mineral_req = self.nrc.calculate_mineral_requirements(
            animal.current_weight_kg,
            animal.is_pregnant
        )

        return {
            "animal_id": animal_id,
            "dry_matter_intake_kg": dmi,
            "energy_requirements": energy_req,
            "protein_requirements": protein_req,
            "mineral_requirements": mineral_req
        }

    def get_available_feeds(self, farm_id: str) -> List[models.Feed]:
        """Çiftlikteki mevcut yemleri getir"""
        return self.db.query(models.Feed).filter(models.Feed.farm_id == farm_id).all()

    def calculate_feed_nutritional_value(self, feed: models.Feed, amount_kg: float) -> Dict:
        """Yem miktarına göre besin değeri hesapla"""
        dry_matter = amount_kg * (feed.dry_matter_percentage / 100)

        return {
            "dry_matter_kg": round(dry_matter, 3),
            "crude_protein_kg": round(dry_matter * (feed.crude_protein_percentage / 100), 3),
            "metabolizable_energy_mcal": round(amount_kg * feed.metabolizable_energy_mcal_kg, 2),
            "calcium_kg": round(dry_matter * (feed.calcium_percentage / 100), 4),
            "phosphorus_kg": round(dry_matter * (feed.phosphorus_percentage / 100), 4),
            "cost": round(amount_kg * feed.cost_per_kg, 2)
        }

    def optimize_ration_simple(self, requirements: Dict, feeds: List[models.Feed],
                              objective: str = "cost") -> Dict:
        """Basit rasyon optimizasyonu (doğrusal yaklaşım)"""

        if not feeds:
            raise ValueError("Optimizasyon için yem bulunamadı")

        target_dmi = requirements["dry_matter_intake_kg"]
        target_energy = requirements["energy_requirements"]["metabolizable_energy"]
        target_protein = requirements["protein_requirements"]["crude_protein"]
        target_calcium = requirements["mineral_requirements"]["calcium"]
        target_phosphorus = requirements["mineral_requirements"]["phosphorus"]

        # Yem kategorilerine ayır
        concentrates = [f for f in feeds if f.feed_type == "concentrate"]
        forages = [f for f in feeds if f.feed_type in ["hay", "silage", "pasture"]]

        if not concentrates or not forages:
            raise ValueError("Optimizasyon için hem konsantre hem de kaba yem gerekli")

        # En iyi konsantre ve kaba yemi seç (basit yaklaşım)
        best_concentrate = max(concentrates, key=lambda f: f.crude_protein_percentage / f.cost_per_kg)
        best_forage = max(forages, key=lambda f: f.metabolizable_energy_mcal_kg / f.cost_per_kg)

        # Başlangıç oranları (%30 konsantre, %70 kaba yem)
        concentrate_ratio = 0.3
        forage_ratio = 0.7

        # İteratif optimizasyon
        best_solution = None
        best_score = float('inf') if objective == "cost" else 0

        for conc_ratio in [0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]:
            forage_ratio = 1.0 - conc_ratio

            conc_amount = target_dmi * conc_ratio
            forage_amount = target_dmi * forage_ratio

            # Besin değerlerini hesapla
            conc_nutrition = self.calculate_feed_nutritional_value(best_concentrate, conc_amount)
            forage_nutrition = self.calculate_feed_nutritional_value(best_forage, forage_amount)

            total_protein = conc_nutrition["crude_protein_kg"] + forage_nutrition["crude_protein_kg"]
            total_energy = conc_nutrition["metabolizable_energy_mcal"] + forage_nutrition["metabolizable_energy_mcal"]
            total_calcium = conc_nutrition["calcium_kg"] + forage_nutrition["calcium_kg"]
            total_phosphorus = conc_nutrition["phosphorus_kg"] + forage_nutrition["phosphorus_kg"]
            total_cost = conc_nutrition["cost"] + forage_nutrition["cost"]

            # Kısıtlamaları kontrol et
            protein_ratio = total_protein / target_protein
            energy_ratio = total_energy / target_energy
            calcium_ratio = total_calcium / target_calcium
            phosphorus_ratio = total_phosphorus / target_phosphorus

            # Minimum gereksinimleri karşılıyor mu?
            if (protein_ratio >= 0.95 and energy_ratio >= 0.95 and
                calcium_ratio >= 0.95 and phosphorus_ratio >= 0.95):

                # Skor hesapla
                if objective == "cost":
                    score = total_cost
                    if score < best_score:
                        best_score = score
                        best_solution = {
                            "concentrate": {
                                "feed": best_concentrate,
                                "amount_kg": conc_amount,
                                "nutrition": conc_nutrition
                            },
                            "forage": {
                                "feed": best_forage,
                                "amount_kg": forage_amount,
                                "nutrition": forage_nutrition
                            },
                            "totals": {
                                "dry_matter_kg": target_dmi,
                                "crude_protein_kg": total_protein,
                                "metabolizable_energy_mcal": total_energy,
                                "calcium_kg": total_calcium,
                                "phosphorus_kg": total_phosphorus,
                                "cost_per_day": total_cost
                            },
                            "ratios": {
                                "protein_adequacy": protein_ratio,
                                "energy_adequacy": energy_ratio,
                                "calcium_adequacy": calcium_ratio,
                                "phosphorus_adequacy": phosphorus_ratio
                            }
                        }

        if not best_solution:
            raise ValueError("Mevcut yemlerle gereksinimler karşılanamıyor")

        return best_solution

    def create_ration_record(self, farm_id: str, animal_id: str, ration_data: Dict,
                           name: str, description: str = None) -> models.Ration:
        """Optimizasyon sonucunu veritabanına kaydet"""

        # Rasyon kaydı oluştur
        ration = models.Ration(
            farm_id=farm_id,
            animal_id=animal_id,
            name=name,
            description=description,
            ration_type="individual",
            total_cost_per_day=ration_data["totals"]["cost_per_day"],
            total_dry_matter_kg=ration_data["totals"]["dry_matter_kg"],
            total_crude_protein_percentage=(ration_data["totals"]["crude_protein_kg"] /
                                          ration_data["totals"]["dry_matter_kg"] * 100),
            total_metabolizable_energy_mcal=ration_data["totals"]["metabolizable_energy_mcal"],
            total_calcium_percentage=(ration_data["totals"]["calcium_kg"] /
                                    ration_data["totals"]["dry_matter_kg"] * 100),
            total_phosphorus_percentage=(ration_data["totals"]["phosphorus_kg"] /
                                       ration_data["totals"]["dry_matter_kg"] * 100),
            is_optimized=True,
            optimization_objective="cost",
            optimization_score=ration_data["totals"]["cost_per_day"]
        )

        self.db.add(ration)
        self.db.flush()  # ID'yi al

        # Rasyon bileşenlerini ekle
        components = []

        for component_type in ["concentrate", "forage"]:
            if component_type in ration_data:
                comp_data = ration_data[component_type]

                component = models.RationComponent(
                    ration_id=ration.id,
                    feed_id=comp_data["feed"].id,
                    amount_kg_per_day=comp_data["amount_kg"],
                    percentage_of_total_dm=(comp_data["amount_kg"] / ration_data["totals"]["dry_matter_kg"] * 100),
                    dry_matter_contribution_kg=comp_data["nutrition"]["dry_matter_kg"],
                    protein_contribution_kg=comp_data["nutrition"]["crude_protein_kg"],
                    energy_contribution_mcal=comp_data["nutrition"]["metabolizable_energy_mcal"],
                    calcium_contribution_kg=comp_data["nutrition"]["calcium_kg"],
                    phosphorus_contribution_kg=comp_data["nutrition"]["phosphorus_kg"],
                    cost_contribution=comp_data["nutrition"]["cost"]
                )

                components.append(component)
                self.db.add(component)

        self.db.commit()
        return ration
