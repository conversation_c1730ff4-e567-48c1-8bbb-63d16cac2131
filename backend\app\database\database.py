from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os

# SQLite veritabanı URL'si
SQLALCHEMY_DATABASE_URL = "sqlite:///./hayvancilik.db"

# SQLAlchemy engine oluştur
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False}  # SQLite için gerekli
)

# Session factory oluştur
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base sınıfı
Base = declarative_base()

# Veritabanı session dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Veritabanı tablolarını oluştur
def create_tables():
    try:
        from .models import Base
        Base.metadata.create_all(bind=engine)
        print("✓ Veritabanı tablolarını oluşturuldu")
    except Exception as e:
        print(f"✗ Veritabanı oluşturma hatası: {e}")

# Veritabanını sıfırla (geliştirme için)
def reset_database():
    from .models import Base
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
