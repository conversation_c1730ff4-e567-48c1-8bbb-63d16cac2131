'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Calculator, 
  ArrowLeft, 
  AlertCircle, 
  CheckCircle,
  TrendingUp,
  DollarSign,
  Target
} from 'lucide-react';
import { rationApi, farmApi, animalApi } from '@/services/api';
import { Farm, Animal, NutritionalRequirement, OptimizationResult } from '@/types';

export default function OptimizeRationPage() {
  const router = useRouter();
  const [farms, setFarms] = useState<Farm[]>([]);
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [selectedFarmId, setSelectedFarmId] = useState<string>('');
  const [selectedAnimalId, setSelectedAnimalId] = useState<string>('');
  const [rationName, setRationName] = useState<string>('');
  const [rationDescription, setRationDescription] = useState<string>('');
  const [objective, setObjective] = useState<'cost' | 'performance' | 'balanced'>('cost');
  const [requirements, setRequirements] = useState<NutritionalRequirement | null>(null);
  const [optimizationResult, setOptimizationResult] = useState<OptimizationResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [calculatingRequirements, setCalculatingRequirements] = useState(false);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    loadFarms();
  }, []);

  useEffect(() => {
    if (selectedFarmId) {
      loadAnimals();
    }
  }, [selectedFarmId]);

  useEffect(() => {
    if (selectedAnimalId) {
      calculateRequirements();
    }
  }, [selectedAnimalId]);

  const loadFarms = async () => {
    try {
      const farmsData = await farmApi.getFarms();
      setFarms(farmsData);
      if (farmsData.length > 0) {
        setSelectedFarmId(farmsData[0].id);
      }
    } catch (err) {
      setError('Çiftlikler yüklenirken hata oluştu');
      console.error(err);
    }
  };

  const loadAnimals = async () => {
    if (!selectedFarmId) return;
    
    try {
      const animalsData = await animalApi.getAnimalsByFarm(selectedFarmId);
      setAnimals(animalsData);
      setSelectedAnimalId('');
      setRequirements(null);
      setOptimizationResult(null);
    } catch (err) {
      setError('Hayvanlar yüklenirken hata oluştu');
      console.error(err);
    }
  };

  const calculateRequirements = async () => {
    if (!selectedAnimalId) return;
    
    setCalculatingRequirements(true);
    setError('');
    
    try {
      const requirementsData = await rationApi.calculateRequirements(selectedAnimalId);
      setRequirements(requirementsData);
      
      // Otomatik rasyon adı oluştur
      const selectedAnimal = animals.find(a => a.id === selectedAnimalId);
      if (selectedAnimal) {
        setRationName(`${selectedAnimal.breed} - ${selectedAnimal.current_weight_kg}kg - ${new Date().toLocaleDateString('tr-TR')}`);
      }
    } catch (err) {
      setError('Besin ihtiyaçları hesaplanırken hata oluştu');
      console.error(err);
    } finally {
      setCalculatingRequirements(false);
    }
  };

  const handleOptimize = async () => {
    if (!selectedFarmId || !selectedAnimalId || !rationName.trim()) {
      setError('Lütfen tüm gerekli alanları doldurun');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const result = await rationApi.optimizeRation({
        farm_id: selectedFarmId,
        animal_id: selectedAnimalId,
        objective,
        name: rationName.trim(),
        description: rationDescription.trim() || undefined
      });

      setOptimizationResult(result);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Rasyon optimizasyonu sırasında hata oluştu');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount);
  };

  const getAdequacyColor = (ratio: number) => {
    if (ratio >= 1.0) return 'text-green-600';
    if (ratio >= 0.95) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getAdequacyText = (ratio: number) => {
    if (ratio >= 1.0) return 'Yeterli';
    if (ratio >= 0.95) return 'Sınırda';
    return 'Yetersiz';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => router.back()}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft className="h-5 w-5 text-gray-600" />
        </button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Rasyon Optimizasyonu</h1>
          <p className="text-gray-600 mt-2">
            Hayvan için optimal rasyon hesaplaması yapın
          </p>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Column - Input Form */}
        <div className="space-y-6">
          {/* Farm and Animal Selection */}
          <div className="content-overlay p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Hayvan Seçimi
            </h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Çiftlik
                </label>
                <select
                  value={selectedFarmId}
                  onChange={(e) => setSelectedFarmId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="">Çiftlik seçin</option>
                  {farms.map((farm) => (
                    <option key={farm.id} value={farm.id}>
                      {farm.name} - {farm.location}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Hayvan
                </label>
                <select
                  value={selectedAnimalId}
                  onChange={(e) => setSelectedAnimalId(e.target.value)}
                  disabled={!selectedFarmId || animals.length === 0}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 disabled:bg-gray-100"
                >
                  <option value="">Hayvan seçin</option>
                  {animals.map((animal) => (
                    <option key={animal.id} value={animal.id}>
                      {animal.breed} - {animal.current_weight_kg}kg - {animal.age_months} ay
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Ration Details */}
          <div className="content-overlay p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Rasyon Detayları
            </h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Rasyon Adı *
                </label>
                <input
                  type="text"
                  value={rationName}
                  onChange={(e) => setRationName(e.target.value)}
                  placeholder="Rasyon adı girin"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Açıklama
                </label>
                <textarea
                  value={rationDescription}
                  onChange={(e) => setRationDescription(e.target.value)}
                  placeholder="Rasyon açıklaması (opsiyonel)"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Optimizasyon Hedefi
                </label>
                <select
                  value={objective}
                  onChange={(e) => setObjective(e.target.value as any)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="cost">Maliyet Minimizasyonu</option>
                  <option value="performance">Performans Maksimizasyonu</option>
                  <option value="balanced">Dengeli Yaklaşım</option>
                </select>
              </div>
            </div>
          </div>

          {/* Optimize Button */}
          <button
            onClick={handleOptimize}
            disabled={!requirements || loading || !rationName.trim()}
            className="w-full btn-primary text-white py-3 rounded-lg flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Optimize Ediliyor...</span>
              </>
            ) : (
              <>
                <Calculator className="h-5 w-5" />
                <span>Rasyonu Optimize Et</span>
              </>
            )}
          </button>
        </div>

        {/* Right Column - Results */}
        <div className="space-y-6">
          {/* Nutritional Requirements */}
          {calculatingRequirements ? (
            <div className="content-overlay p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Besin ihtiyaçları hesaplanıyor...</p>
            </div>
          ) : requirements ? (
            <div className="content-overlay p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Besin İhtiyaçları
              </h2>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Kuru Madde Alımı:</span>
                  <span className="font-medium">{requirements.dry_matter_intake_kg.toFixed(1)} kg/gün</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Metabolik Enerji:</span>
                  <span className="font-medium">{requirements.energy_requirements.metabolizable_energy.toFixed(1)} Mcal/gün</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Ham Protein:</span>
                  <span className="font-medium">{(requirements.protein_requirements.crude_protein * 1000).toFixed(0)} g/gün</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Kalsiyum:</span>
                  <span className="font-medium">{(requirements.mineral_requirements.calcium * 1000).toFixed(1)} g/gün</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Fosfor:</span>
                  <span className="font-medium">{(requirements.mineral_requirements.phosphorus * 1000).toFixed(1)} g/gün</span>
                </div>
              </div>
            </div>
          ) : selectedAnimalId ? (
            <div className="content-overlay p-6 text-center">
              <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Hayvan seçildi, besin ihtiyaçları hesaplanıyor...</p>
            </div>
          ) : (
            <div className="content-overlay p-6 text-center">
              <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Besin ihtiyaçlarını görmek için hayvan seçin</p>
            </div>
          )}

          {/* Optimization Results */}
          {optimizationResult && (
            <div className="content-overlay p-6">
              <div className="flex items-center space-x-2 mb-4">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <h2 className="text-lg font-semibold text-gray-900">
                  Optimizasyon Sonucu
                </h2>
              </div>
              
              <div className="space-y-4">
                <div className="bg-green-50 p-4 rounded-lg">
                  <p className="text-green-800 font-medium mb-2">{optimizationResult.message}</p>
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-green-700">
                      Günlük Maliyet: {formatCurrency(optimizationResult.optimization_result.total_cost_per_day)}
                    </span>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Rasyon Bileşenleri:</h3>
                  <div className="space-y-2">
                    {optimizationResult.optimization_result.components.map((component, index) => (
                      <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100">
                        <span className="text-sm text-gray-600">{component.feed_name}:</span>
                        <div className="text-right">
                          <div className="font-medium">{component.amount_kg.toFixed(1)} kg</div>
                          <div className="text-xs text-gray-500">{formatCurrency(component.cost)}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Besin Yeterliliği:</h3>
                  <div className="space-y-2">
                    {Object.entries(optimizationResult.optimization_result.adequacy_ratios).map(([key, ratio]) => (
                      <div key={key} className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">
                          {key === 'protein_adequacy' ? 'Protein' :
                           key === 'energy_adequacy' ? 'Enerji' :
                           key === 'calcium_adequacy' ? 'Kalsiyum' : 'Fosfor'}:
                        </span>
                        <span className={`font-medium ${getAdequacyColor(ratio as number)}`}>
                          {((ratio as number) * 100).toFixed(0)}% ({getAdequacyText(ratio as number)})
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <button
                  onClick={() => router.push('/rations')}
                  className="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition-colors"
                >
                  Rasyonları Görüntüle
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
